<?php

namespace App\Http\Controllers\API\agence;

use App\Http\Controllers\API\helpers\CommonController;
use App\Models\Agency;
use Illuminate\Http\Request;

class AgencyController extends CommonController
{

    /**
     * get_agency_detail function
     * Get all details of the agency
     * @param Request $request
     * @return JsonResponse $response
     */
    public function get_agency_detail(Request $request){
        $response = [];
        $status = 200;
        try {
            $personal = $this->get_personal_auth($request);
            if ($personal == null) {
                $response = [
                    'success'=>false,
                    'message'=>"Echec de validation des données",
                    'result'=>null,
                    'errors'=>"Personnel introuvable"
                ];
            } else {
                $agency = $personal->currentAgency()->first();
                $data = Agency::where('id',$agency->id)->with([
                    'city','quarter','personals','clients','subscriptions','responsable','parent','annexes',
                    'wallet','transactions','collectors','currentCollectors','supervisors','cashiers',
                    'currentSupervisors','currentCashiers','cotisations','versements',
                    'collectors'=>function($query){
                        $query->with([
                            'currentAgency','user','user','role','city','quarter','versements',
                            'cotisations','payments','clients','subscriptions',
                        ]);
                    },
                    'currentCollectors'=>function($query){
                        $query->with([
                            'currentAgency','user','user','role','city','quarter','versements',
                            'cotisations','payments','clients','subscriptions',
                        ]);
                    },
                    'supervisors'=>function($query){
                        $query->with([
                            'currentAgency','user','user','role','city','quarter',
                        ]);
                    },
                    'currentSupervisors'=>function($query){
                        $query->with([
                            'currentAgency','user','user','role','city','quarter',
                        ]);
                    },
                    'cashiers'=>function($query){
                        $query->with([
                            'currentAgency','user','user','role','city','quarter','versements'
                        ]);
                    },
                    'currentCashiers'=>function($query){
                        $query->with([
                            'currentAgency','user','user','role','city','quarter','versements'
                        ]);
                    },
                    'city'=>function($query){
                        $query->with(['country']);
                    },
                    'quarter'=>function($query){
                        $query->with(['city']);
                    },
                    'responsable'=>function($query){
                        $query->with(['user','role','city','quarter']);
                    },
                    'parent'=>function($query){
                        $query->with(['city','quarter']);
                    },
                    'annexes'=>function($query){
                        $query->with(['city','quarter']);
                    },
                    'personals'=>function($query){
                        $query->with(['user','role','city','quarter']);
                    },
                    'clients'=>function($query){
                        $query->with(['user','city','quarter','subscriptions']);
                    },
                    'subscriptions'=>function($query){
                        $query->with(['client','agency','cotisation','payments']);
                    },
                    'cotisations'=>function($query){
                        $query->with(['agency','subscription','payments']);
                    },
                ])->first();
                $response = [
                    'success'=>true,
                    'message'=>"Détail de l'agence",
                    'result'=>$data
                ];
            }
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la récupération des détails de l'agence",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }
    /**
     * get_agency_personals function
     * Get all personals of the agency
     * @param Request $request
     * @return void
     */
    public function get_agency_personals(Request $request){
        $response = [];
        $status = 200;
        try {
            $personal = $this->get_personal_auth($request);

            if ($personal == null) {
                $response = [
                    'success'=>false,
                    'message'=>"Echec de validation des données",
                    'result'=>null,
                    'errors'=>"Personnel introuvable"
                ];
            } else {
                $agency = $personal->currentAgency()->first();
                $personals = $agency->personals()->with(['role','city','quarter'])->get();
                $response = [
                    'success'=>true,
                    'message'=>"Liste des personnels de l'agence",
                    'result'=>[
                        'agency'=>$agency,
                        'personals'=>$personals
                    ]
                ];
            }
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la récupération des personnels de l'agence",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * get_agency_clients function
     *  Get all clients of the agency
     * @param Request $request
     * @return void
     */
    public function get_agency_clients(Request $request){
        $response = [];
        $status = 200;
        try {
            $personal = $this->get_personal_auth($request);
            if ($personal == null) {
                $response = [
                    'success'=>false,
                    'message'=>"Echec de validation des données",
                    'result'=>null,
                    'errors'=>"Personnel introuvable"
                ];
            } else {
                $agency = $personal->currentAgency()->first();
                $clients = $agency->clients()->with(['city','quarter','subscriptions'])->get();
                $response = [
                    'success'=>true,
                    'message'=>"Liste des clients de l'agence",
                    'result'=>$clients
                ];
            }
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la récupération des clients de l'agence",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * get_agency_subscriptions function
     * Get all subscriptions of the agency
     * @param Request $request
     * @return void
     */
    public function get_agency_subscriptions(Request $request){
        $response = [];
        $status = 200;
        try {
            $personal = $this->get_personal_auth($request);
            if ($personal == null) {
                $response = [
                    'success'=>false,
                    'message'=>"Echec de validation des données",
                    'result'=>null,
                    'errors'=>"Personnel introuvable"
                ];
            } else {
                $agency = $personal->currentAgency()->first();
                $subscriptions = $agency->subscriptions()->with(['client','pack','collector','payments'])->get();
                $response = [
                    'success'=>true,
                    'message'=>"Liste des abonnements de l'agence",
                    'result'=>[
                        'agency'=>$agency,
                        'subscriptions'=>$subscriptions
                    ]
                ];
            }
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la récupération des abonnements de l'agence",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * get_agency_cotisations function
     *  Get all cotisations of the agency
     * @param Request $request
     * @return void
     */
    public function get_agency_cotisations(Request $request){
        $response = [];
        $status = 200;
        try {
            $personal = $this->get_personal_auth($request);
            if ($personal == null) {
                $response = [
                    'success'=>false,
                    'message'=>"Echec de validation des données",
                    'result'=>null,
                    'errors'=>"Personnel introuvable"
                ];
            } else {
                $agency = $personal->currentAgency()->first();
                $cotisations = $agency->cotisations()->get();
                $response = [
                    'success'=>true,
                    'message'=>"Liste des cotisations de l'agence",
                    'result'=>[
                        'agency'=>$agency,
                        'cotisations'=>$cotisations
                    ]
                ];
            }
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la récupération des cotisations de l'agence",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }
}
