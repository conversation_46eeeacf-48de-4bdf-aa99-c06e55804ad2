<?php

use Illuminate\Support\Str;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('versements', function (Blueprint $table) {
            $table->id();
            $table->integer('collector_id')->nullable()->comment("L'agent (collecteur) qui initie le versement");
            $table->integer('cashier_id')->nullable()->comment("L'agent (caissier) qui traitera le versement");
            $table->double('amount');
            $table->double('amount_remaining')->default(0)->nullable();
            $table->timestamp('payment_date')->nullable();
            $table->timestamp('confirmed_at')->nullable();
            $table->timestamp('expired_at')->nullable();
            $table->integer('agency_id')->nullable();
            $table->enum('status',['initialized','in_progress','completed','confirmed'])->default('initialized');
            $table->string('token')->default(Str::uuid());
            $table->string('qrcode')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('versements');
    }
};
