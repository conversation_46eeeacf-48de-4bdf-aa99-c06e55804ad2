<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class QuarterTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $quarters = [
            [
                'name' => 'Adakpamé',
                'city_id' => 1,
            ],
            [
                'name' => 'Adidogomé',
                'city_id' => 1,
            ],
            [
                'name' => 'Agbalépédogan',
                'city_id' => 1,
            ],
            [
                'name' => 'Agbalepedo',
                'city_id' => 1,
            ],
            [
                'name' => 'La pampa',
                'city_id' => 1,
            ],
            [
                'name' => 'Awatamé',
                'city_id' => 1,
            ],
            [
                'name' => 'Avénou',
                'city_id' => 1,
            ],
            [
                'name' => 'Avédji',
                'city_id' => 1,
            ],
            [
                'name' => 'Sodabidapé',
                'city_id' => 1,
            ],
            [
                'name' => 'Soviépé',
                'city_id' => 1,
            ],
            [
                'name' => 'Yokoè',
                'city_id' => 1,
            ],
            [
                'name' => 'Wonyomé',
                'city_id' => 1,
            ],
            [
                'name' => 'Djidjolé',
                'city_id' => 1,
            ],
            [
                'name' => 'Sagbado',
                'city_id' => 1,
            ],
            [
                'name' => 'Tokoin',
                'city_id' => 1,
            ],
            [
                'name' => 'Novissi',
                'city_id' => 1,
            ],
            [
                'name' => 'Campus',
                'city_id' => 1,
            ],
            [
                'name' => 'Hédzranawoé',
                'city_id' => 1,
            ],
            [
                'name' => 'Massalassi',
                'city_id' => 1,
            ],
            [
                'name' => 'Agoè cacaveli',
                'city_id' => 1,
            ],
            [
                'name' => 'Assiyéyé',
                'city_id' => 1,
            ],
            [
                'name' => 'Atikoumé',
                'city_id' => 1,
            ],
            [
                'name' => 'Atigangomé',
                'city_id' => 1,
            ],
            [
                'name' => 'Amadahomé',
                'city_id' => 1,
            ],
            [
                'name' => 'Madiba',
                'city_id' => 1,
            ],
            [
                'name' => 'Totsi',
                'city_id' => 1,
            ],
            [
                'name' => 'Adewi',
                'city_id' => 1,
            ],
            [
                'name' => 'Lomégan',
                'city_id' => 1,
            ],
            [
                'name' => 'Adidoadin',
                'city_id' => 1,
            ],
            [
                'name' => 'Limousine',
                'city_id' => 1,
            ],
            [
                'name' => 'Cassablanca',
                'city_id' => 1,
            ],
            [
                'name' => 'Adétikopé',
                'city_id' => 1,
            ],
            [
                'name' => 'Baguida',
                'city_id' => 1,
            ],
            [
                'name' => 'Nukafu',
                'city_id' => 1,
            ],
            [
                'name' => 'Klikamé',
                'city_id' => 1,
            ],
            [
                'name' => 'Gblinkomé',
                'city_id' => 1,
            ],
            [
                'name' => 'Gbossimé',
                'city_id' => 1,
            ],
            [
                'name' => '3k',
                'city_id' => 1,
            ],
            [
                'name' => 'SOS village d\'enfants',
                'city_id' => 1,
            ],
            [
                'name' => 'Zanguera',
                'city_id' => 1,
            ],
            [
                'name' => 'Klémé',
                'city_id' => 1,
            ],
            [
                'name' => 'Ségbé',
                'city_id' => 1,
            ],
            [
                'name' => 'Kégué',
                'city_id' => 1,
            ],
        ];

        foreach ($quarters as $quarter) {
            \App\Models\Quarter::create($quarter);
        }
    }
}
