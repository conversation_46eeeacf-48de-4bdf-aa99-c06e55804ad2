<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClientPerformance extends Model
{
    use HasFactory;
    protected $fillable = [
        'id',
        'nom',
        'prenoms',
        'phone',
        'email',
        'finished_subscriptions_count',
        'subscription_revenue',
        'finished_cotisations_count',
        'cotisation_revenue',
        'total_revenue',
        'engagement_score',
        'last_updated'
    ];

    public $incrementing = false;

    public function client()
    {
        return $this->belongsTo(Client::class, 'id');
    }
}
