<?php
namespace App\Console\Commands;

use App\Models\PackPerformance;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RefreshPackPerformance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'refresh:pack';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Recalcule les statistiques des packs actifs et met à jour la table pack_performances";

    /**
     * Execute the console command.
     */

    public function handle()
    {
        $this->info("🔍 Recherche des packs impactés...");

        // Vérifie si la table est vide (premier lancement)
        $isEmpty = PackPerformance::count() === 0;

        if ($isEmpty) {
            $this->info("🆕 Aucune donnée trouvée. Mise à jour complète...");
            $idsToUpdate = [];
        } else {
            // Récupérer les IDs des packs impactés par des modifications récentes
            $recentPackIds = DB::select("
                SELECT DISTINCT p.id
                FROM packs p
                WHERE p.updated_at > NOW() - INTERVAL 24 HOUR

                UNION

                SELECT DISTINCT s.pack_id
                FROM subscriptions s
                WHERE s.updated_at > NOW() - INTERVAL 24 HOUR

                UNION

                SELECT DISTINCT co.subscription_id AS id
                FROM cotisations co
                WHERE co.updated_at > NOW() - INTERVAL 24 HOUR
            ");

            if (empty($recentPackIds)) {
                $this->info("✅ Aucun pack à mettre à jour.");
                return;
            }

            $idsToUpdate = array_map(fn($row) => $row->id, $recentPackIds);
            $this->info("♻️ " . count($idsToUpdate) . " packs trouvés. Mise à jour en cours...");
        }

        // Supprimer les anciennes données pour ces packs
        if (! empty($idsToUpdate)) {
            PackPerformance::whereIn('id', $idsToUpdate)->delete();
        } else {
            PackPerformance::truncate();
        }

        // Construire la requête SQL pour récupérer les données brutes
        $query = "
            SELECT
                p.id,
                p.name AS pack_name,
                p.carnet_price,
                COUNT(DISTINCT s.id) AS total_subscriptions,
                SUM(CASE WHEN s.status IN ('finished', 'delivered') THEN 1 ELSE 0 END) AS successful_subscriptions,
                ROUND(COALESCE(SUM(s.carnet_price), 0), 2) AS subscription_revenue,
                COUNT(DISTINCT co.id) AS total_cotisations,
                SUM(CASE WHEN co.status = 'finished' THEN 1 ELSE 0 END) AS completed_cotisations,
                ROUND(COALESCE(SUM(co.total_amount), 0), 2) AS cotisation_revenue,
                ROUND(
                    (COALESCE(COUNT(DISTINCT s.id), 0) * 0.5) +
                    (COALESCE(SUM(CASE WHEN s.status IN ('finished', 'delivered') THEN 1 ELSE 0 END), 0) * 0.3) +
                    ((ROUND(COALESCE(SUM(s.carnet_price), 0), 2) / 10000) * 0.2)
                , 2) AS performance_score
            FROM packs p
            LEFT JOIN subscriptions s ON p.id = s.pack_id
            LEFT JOIN cotisations co ON s.id = co.subscription_id
        ";

        if (! empty($idsToUpdate)) {
            $query .= " WHERE p.id IN (" . implode(',', $idsToUpdate) . ")";
        }

        $query .= " GROUP BY p.id, p.name, p.carnet_price";

        // Exécution de la requête
        $results = DB::select($query);

        if (empty($results)) {
            $this->info("✅ Aucun résultat à insérer.");
            return;
        }

        // Calcul du score maximum pour normalisation
        $scores   = array_map(fn($row) => $row->performance_score, $results);
        $maxScore = max($scores);

        // Insertion avec normalisation du score
        foreach ($results as $row) {
            $normalizedScore = $maxScore > 0 ? round(($row->performance_score / $maxScore) * 100, 2) : 0;

            // Limiter à 100 au maximum
            $normalizedScore = min(100.00, $normalizedScore);

            PackPerformance::create(array_merge((array) $row, [
                'performance_score' => $normalizedScore,
                'last_updated'      => now(),
            ]));
        }

        if ($isEmpty) {
            $this->info("✅ Initialisation terminée : " . count($results) . " packs ajoutés !");
        } else {
            $this->info("✅ " . count($results) . " packs mis à jour !");
        }
    }
}
