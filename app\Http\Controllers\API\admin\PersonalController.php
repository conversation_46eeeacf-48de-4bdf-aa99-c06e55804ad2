<?php

namespace App\Http\Controllers\API\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\API\helpers\HelperController;
use App\Models\AgencyPersonal;
use App\Models\Personal;
use App\Models\User;
use App\Models\Wallet;
use App\Rules\PersonalRule;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Hash;
use App\Services\FirebaseService;
use Illuminate\Support\Facades\DB;

class PersonalController extends HelperController
{
    protected $firebaseService;

    public function __construct(FirebaseService $firebaseService)
    {
        $this->firebaseService = $firebaseService;
    }


    /**
     * get_all_personals function
     * Get all personals
     * @param Request $request
     * @return void
     */
    public function get_all_personals(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $page = $request->input('page', 1);
            $perPage = $request->input('limit', 15);
            $personals = Personal::whereNotIn('role_id',[1,2])->with([
                'user',
                'quarter',
                'city',
                'role',
                'currentAgency',
            ])->paginate($perPage, ['*'], 'page', $page);
            $response = [
                'success' => true,
                'message' => "Liste des personnels",
                'result' => $personals
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des personnels",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * create_personal function
     * Ajouter un personnel
     * @param Request $request
     * @return void
     */
    public function add_personals(Request $request)
    {
        $response = [];
        $status = 200;
        // return $request->all();
        try {
            $validate = Validator::make($request->all(), [
                'personals' => [
                    'required',
                    'array',
                    new PersonalRule()
                ],
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
                $status = 422;
            } else {
                $personal_info = $request->personals;
                $personals = $request->personals;

                $success = [];
                foreach ($personals as $key => $personal) {
                    $agency_id = $personal['agency_id'];
                    $affected_at = date('Y-m-d H:i');
                    $agency = $this->get_agency($agency_id);

                    $username = $personal['nom'] . ' ' . $personal['prenoms'];
                    $password = Hash::make($personal['phone']);
                    $checkUser = User::where('username', $username)->where('phone', $personal['phone'])->first();
                    if ($checkUser !== null) {
                        $response = [
                            'success' => false,
                            'message' => "Utilisateur déjà existant",
                            'result' => null
                        ];
                        $status = 400;
                        return $this->apiResponse($response, $status);
                    }
                    $secret_code = rand(1000, 9999);

                    $user = User::create([
                        'username' => $username,
                        'password' => $password,
                        'phone' => $personal['phone'],
                        'email' => $personal['email'],
                        'role_id' => $personal['role_id'],
                        'status' => 1,
                        'secret_code' => Crypt::encryptString($secret_code)
                    ]);
                    if ($user !== null) {
                        $newPersonal = Personal::create([
                            'role_id' => $personal['role_id'],
                            'quarter_id' => $personal['quarter_id'],
                            'city_id' => $personal['city_id'],
                            'status' => 1,
                            'nom' => $personal['nom'],
                            'prenoms' => $personal['prenoms'],
                            'email' => $personal['email'],
                            'phone' => $personal['phone'],
                            'date_nsce' => $personal['date_nsce'],
                            'lieu_nsce' => $personal['lieu_nsce'],
                            'situation_matrimoniale' => $personal['situation_matrimoniale'],
                            'gender' => $personal['gender'],
                            'is_affected' => 1,
                            'role_id' => $personal['role_id'],
                            'user_id' => $user->id,
                        ]);
                        if ($newPersonal !== null) {
                            $agency->personals()->attach($newPersonal->id, [
                                'role_id' => $personal['role_id'],
                                'affected_at' => $affected_at,
                                'mutated_at' => null,
                                'is_current' => 1
                            ]);
                            $wallet_code = rand(1000, 9999) . " " . rand(1000, 9999) . " " . rand(1000, 9999) . " " . rand(1000, 9999);
                            $wallet_code = str_replace(' ', '', $wallet_code);
                            $wallet = Wallet::create([
                                'user_id' => $user->id,
                                'agency_id' => $agency->agency_id,
                                'balance' => 0,
                                'type' => 'agent',
                                'code' => str_shuffle($wallet_code)
                            ]);
                            $data[] = [
                                'personal' => $newPersonal,
                                'user' => $user,
                                'secret_code' => $secret_code,
                                'wallet' => $wallet
                            ];
                        }
                    }
                }
                $response = [
                    'success' => true,
                    'message' => "Personnels ajoutés avec succès",
                    'result' => $success
                ];
            }
        } catch (\Throwable $th) {
            $status = 500;
            $response = [
                'success' => false,
                'message' => "Echec de création du personnel",
                'result' => null,
                'errors' => $th->getMessage()
            ];
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * create_personal function
     * Ajouter un personnel
     * @param Request $request
     * @return void
     */
    public function addNewPersonal(Request $request)
    {
        $response = [];
        $status = 200;

        try {
            $validate = Validator::make($request->all(), [
                'personal' => [
                    'required',
                    'array',
                    new PersonalRule()
                ],
            ]);

            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
                $status = 422;
            } else {
                $personal = $request->personal;
                $agency_id = $personal['agency_id'];
                $affected_at = date('Y-m-d H:i');
                $agency = $this->get_agency($agency_id);

                $username = $personal['nom'] . ' ' . $personal['prenoms'];
                $password = Hash::make($personal['phone']);
                $checkUser = User::where('username', $username)->where('phone', $personal['phone'])->first();

                if ($checkUser !== null) {
                    $response = [
                        'success' => false,
                        'message' => "Utilisateur déjà existant",
                        'result' => null
                    ];
                    $status = 400;
                    return $this->apiResponse($response, $status);
                }
                DB::beginTransaction();
                try {
                    $secret_code = rand(1000, 9999);

                    $user = User::create([
                        'username' => $username,
                        'password' => $password,
                        'phone' => $personal['phone'],
                        'email' => $personal['email'],
                        'role_id' => $personal['role_id'],
                        'status' => 1,
                        'secret_code' => Crypt::encryptString($secret_code)
                    ]);

                    if ($user !== null) {
                        $newPersonal = Personal::create([
                            'role_id' => $personal['role_id'],
                            'quarter_id' => $personal['quarter_id'],
                            'city_id' => $personal['city_id'],
                            'status' => 1,
                            'nom' => $personal['nom'],
                            'prenoms' => $personal['prenoms'],
                            'email' => $personal['email'],
                            'phone' => $personal['phone'],
                            'date_nsce' => $personal['date_nsce'],
                            'lieu_nsce' => $personal['lieu_nsce'],
                            'situation_matrimoniale' => $personal['situation_matrimoniale'],
                            'gender' => $personal['gender'],
                            'is_affected' => 1,
                            'role_id' => $personal['role_id'],
                            'user_id' => $user->id,
                        ]);

                        if ($newPersonal !== null) {
                            $agency->personals()->attach($newPersonal->id, [
                                'role_id' => $personal['role_id'],
                                'affected_at' => $affected_at,
                                'mutated_at' => null,
                                'is_current' => 1
                            ]);

                            $wallet_code = rand(1000, 9999) . " " . rand(1000, 9999) . " " . rand(1000, 9999) . " " . rand(1000, 9999);
                            $wallet_code = str_replace(' ', '', $wallet_code);
                            $wallet = Wallet::create([
                                'user_id' => $user->id,
                                'agency_id' => $agency->agency_id,
                                'balance' => 0,
                                'type' => 'agent',
                                'code' => str_shuffle($wallet_code)
                            ]);

                            DB::commit();

                            $response = [
                                'success' => true,
                                'message' => "Personnel ajouté avec succès",
                                'result' => [
                                    'personal' => $newPersonal,
                                    'user' => $user,
                                    'secret_code' => $secret_code,
                                    'wallet' => $wallet
                                ]
                            ];
                        }
                    }else{
                        DB::rollback();
                        $response = [
                            'success' => false,
                            'message' => "Echec de la création du personnel",
                            'result' => null,
                            'errors' => null
                        ];
                    }
                } catch (\Throwable $th) {
                    DB::rollback();
                    $response = [
                        'success' => false,
                        'message' => "Echec de la création du personnel",
                        'result' => null,
                        'errors' => $th->getMessage()
                    ];
                }
            }
        } catch (\Throwable $th) {
            $status = 500;
            $response = [
                'success' => false,
                'message' => "Echec de création du personnel",
                'result' => null,
                'errors' => $th->getMessage()
            ];
        }

        return $this->apiResponse($response, $status);
    }


    /**
     * get_personals_not_affected function
     *  Retourner la liste des personnels non affecté
     * @param Request $request
     * @return JsonResponse $response
     */
    public function get_personals_not_affected(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $role_id = $request->role_id;
            $mode = $request->mode;
            // $role = null;
            $message = "Liste des personnels non affectés";
            $personals = Personal::where('is_affected', false)->where('role_id', $role_id)->get();
            if ($mode !== "affectation") {
                $personals = Personal::where('is_affected', true)->where('role_id', $role_id)->get();
                $message = "Liste des personnels affectés";
            }
            $response = [
                'success' => true,
                'message' => $message,
                'result' => $personals
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des personnels non affectés",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * get_personals_by_role function
     * Retourner la liste des personnels par role
     * @param Request $request
     * @return JsonResponse $response
     */
    public function get_personals_by_role(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $page = $request->input('page', 1);
            $perPage = $request->input('limit', 15);
            $role_id = $request->input('role_id');
            if ($role_id !== null) {
                $personals = Personal::where('role_id', $role_id)->with(['user', 'currentAgency', 'quarter', 'city'])->paginate($perPage, ['*'], 'page', $page);

                $response = [
                    'success' => true,
                    'message' => "Liste des personnels",
                    'result' => $personals
                ];
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des personnels",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * create_personals_affectation function
     *  Affecter un personnel à une agence
     * @param Request $request
     * @return void
     */
    public function create_personals_affectation(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'agency_id' => 'required|exists:agencies,id',
                'personals' => 'required|array',
                'mode' => 'required|in:affectation,mutation'
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec lors de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
            } else {
                $mode = $request->mode;
                $affected_at = ($mode == 'affectation') ? date('Y-m-d H:i') : null;
                $mutated_at = ($mode == 'mutation') ? date('Y-m-d H:i') : null;
                $agency = $this->get_agency($request->agency_id);
                $personals = $request->personals;
                $role_id = $request->role_id;
                $data = [];
                foreach ($personals as $key => $personal) {
                    if (isset($personal['personal_id']) && isset($role_id)) {
                        $data_pers = $this->get_personal_by_id($personal['personal_id']);
                        if ($data_pers !== null) {
                            // $check = $agency->personals()->wherePivot('personal_id',$personal['personal_id'])->first();
                            // return $check;
                            $check = AgencyPersonal::where('personal_id', $personal['personal_id'])->where('agency_id', $agency->id)->first();
                            if ($check == null) {

                                $username = $data_pers->nom . ' ' . $data_pers->prenoms;
                                $password = Hash::make($data_pers->phone);
                                $check_user = $data_pers->user;

                                if ($check_user == null) {
                                    $secret_code = rand(1000, 9999);
                                    $user = User::create([
                                        'username' => $username,
                                        'email' => $data_pers->email ?? null,
                                        'phone' => $data_pers->phone,
                                        'password' => $password,
                                        'role_id' => $role_id,
                                        'status' => 1,
                                        'secret_code' => Crypt::encryptString($secret_code)
                                    ]);
                                    if ($user !== null) {
                                        $attach = $agency->personals()->attach($personal['personal_id'], [
                                            'role_id' => $role_id,
                                            'affected_at' => $affected_at,
                                            'mutated_at' => $mutated_at,
                                            'is_current' => 1
                                        ]);
                                        $data_pers->is_affected = 1;
                                        $data_pers->role_id = $role_id;
                                        $data_pers->user_id = $user->id;
                                        $data_pers->save();
                                        $wallet_code = rand(1000, 9999) . " " . rand(1000, 9999) . " " . rand(1000, 9999) . " " . rand(1000, 9999);
                                        $wallet_code = str_replace(' ', '', $wallet_code);
                                        $wallet = Wallet::create([
                                            'user_id' => $user->id,
                                            'agency_id' => $agency->agency_id,
                                            'balance' => 0,
                                            'type' => 'agent',
                                            'code' => str_shuffle($wallet_code)
                                        ]);
                                        $data[] = [
                                            'personal' => $personal,
                                            'user' => $user,
                                            'secret_code' => $secret_code,
                                            'wallet' => $wallet
                                        ];
                                    }
                                } else {
                                    if ($data_pers->user !== null) {
                                        $up_user = $data_pers->user()->update([
                                            'phone' => $data_pers->phone,
                                            'password' => $password,
                                            'role_id' => $personal['role_id'],
                                            'email' => $data_pers->email ?? null
                                        ]);
                                        $user = $data_pers->user;
                                        $data[] = [
                                            'personal' => $data_pers,
                                            'user' => $user,
                                            'secret_code' => null,
                                            'wallet' => $user->wallet
                                        ];
                                    }
                                }
                            } else {
                            }

                            # code...

                        }
                    }
                }
                $response = [
                    'success' => true,
                    'message' => "Personnels affectés avec succès",
                    'result' => $data
                ];
            }
        } catch (\Throwable $th) {
            $status = 500;
            $response = [
                'success' => false,
                'message' => "Echec lors de l'affectation des personnels",
                'result' => null,
                'errors' => $th->getMessage()
            ];
        }
        return $this->apiResponse($response, $status);
    }


    /**
     * get_agency_personals function
     * Récupérer les personnels d'une agence
     * @param Request $request
     * @return void
     */
    public function get_agency_personals(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'agency_id' => 'required|exists:agencies,id',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
            } else {
                $agency = $this->get_agency($request->agency_id);
                if ($agency !== null) {
                    $personals = $agency->personals;
                    $response = [
                        'success' => true,
                        'message' => "Liste des personnels de l'agence " . $agency->name,
                        'result' => $personals
                    ];
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des personnels",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * change_personal_secretCode function
     * Changer le code secret d'un personnel
     * @param Request $request
     * @return void
     */
    public function change_personal_secretCode(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'admin_secret_code' => 'required',
                'user_id' => 'required',
                'secret_code' => 'required',
                'secret_code_confirmation' => 'required',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
            } else {
                $user = $this->get_auth_user($request);
                if ($user == null) {
                    $response = [
                        'success' => false,
                        'message' => "Utilisateur inconnue",
                        'result' => null
                    ];
                } else {
                    $secret_code = Crypt::decryptString($user->secret_code);
                    // dd($secret_code);
                    if ($secret_code !== $request->admin_secret_code) {
                        $response = [
                            'success' => false,
                            'message' => "Code secret inccorrecte",
                            'result' => null
                        ];
                    } else {
                        $personal = Personal::where('user_id', $request->user_id)->with(['user'])->first();
                        if ($personal !== null) {
                            $user_pers = $personal->user()->first();
                            // dd($user_pers);
                            if ($user_pers !== null) {
                                $secret_code = $request->secret_code !== null ? $request->secret_code : rand(1000, 9999);
                                $user_pers->secret_code = Crypt::encryptString($secret_code);
                                $user_pers->save();
                                // $user_pers->update([
                                //     'secret_code'=>Crypt::encrypt($secret_code)
                                // ]);
                                $response = [
                                    'success' => true,
                                    'message' => "Code secret modifié avec succès",
                                    'result' => [
                                        'user' => $user_pers,
                                        'secret_code' => $secret_code
                                    ]
                                ];
                            } else {
                                $response = [
                                    'success' => false,
                                    'message' => "Aucun utilisateur associé à ce personnel",
                                    'result' => null,
                                ];
                            }
                        } else {
                            $response = [
                                'success' => false,
                                'message' => "Aucun personnel trouvé",
                                'result' => null,
                            ];
                        }
                    }
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la modification du code secret",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * update_personal function
     * Modifier les informations d'un personnel
     * @param Request $request
     * @return void
     */
    public function update_personal(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'personal_id' => 'required',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
            } else {
                $personal = Personal::where('id', $request->personal_id)->first();
                if ($personal !== null) {
                    $personal->update([
                        'nom' => $request->nom,
                        'prenoms' => $request->prenoms,
                        'email' => $request->email,
                        'phone' => $request->phone,
                        'role_id' => $request->role_id,
                        'date_nsce' => $request->date_nsce,
                        'lieu_nsce' => $request->lieu_nsce,
                        'gender' => $request->gender,
                        'situation_matrimoniale' => $request->situation_matrimoniale,
                        'city_id' => $request->city_id,
                        'quarter_id' => $request->quarter_id,
                        'localisation' => $request->localisation
                    ]);
                    $user = $personal->user;
                    if ($user !== null) {
                        if ($request->filled('phone') !== $user->phone || $request->filled('email') !== $user->email) {
                            $user->update([
                                'phone' => $request->phone,
                                'email' => $request->email
                            ]);
                        }
                        # code...
                    }
                    $response = [
                        'success' => true,
                        'message' => "Personnel modifié avec succès",
                        'result' => $personal
                    ];
                } else {
                    $response = [
                        'success' => false,
                        'message' => "Aucun personnel trouvé",
                        'result' => null,
                    ];
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la modification du personnel",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * get_details_personal function
     * Récupérer les détails d'un personnel
     * @param Request $request
     * @return void
     */
    public function get_details_personal(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'personal_id' => 'required',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
            } else {
                $personal = Personal::where('id', $request->personal_id)->with([
                    'city',
                    'quarter',
                    'role',
                    'user',
                    'agencies',
                    'currentAgency',
                    'subscriptions',
                    'cotisations',
                    'versements' => function ($query) {
                        $query->with(['collector', 'agency', 'cashier']);
                    },
                    'subscriptions' => function ($query) {
                        $query->with([
                            'client',
                            'cotisation',
                            'payments',
                            'agency',
                            'pack'
                        ]);
                    },
                    'cotisations' => function ($query) {
                        $query->with(['collector', 'client', 'subscription',]);
                    },
                    'clients' => function ($query) {
                        $query->with(['collector', 'packs', 'agency', 'subscriptions', 'city', 'quarter']);
                    },
                    'agencies' => function ($query) {
                        $query->with(['city', 'quarter', 'personals']);
                    },
                ])
                    ->withSum('subscriptions', 'price')
                    ->withSum('cotisations', 'total_amount')
                    ->withSum('versements', 'amount')
                    ->whereId($request->personal_id)
                    ->first();
                if ($personal !== null) {
                    $response = [
                        'success' => true,
                        'message' => "Détails du personnel ",
                        'result' => $personal
                    ];
                } else {
                    $response = [
                        'success' => false,
                        'message' => "Aucun personnel trouvé",
                        'result' => null,
                    ];
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des détails du personnel",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * delete_personal function
     *  Supprimer un personnel
     * @param Request $request
     * @return void
     */
    public function delete_personal(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'personal_id' => 'required',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
            } else {
                $personal = Personal::where('id', $request->personal_id)->where('is_affected', false)->first();
                if ($personal !== null) {
                    $personal->delete();
                    $response = [
                        'success' => true,
                        'message' => "Personnel supprimé avec succès",
                        'result' => $personal
                    ];
                } else {
                    $response = [
                        'success' => false,
                        'message' => "Aucun personnel trouvé",
                        'result' => null,
                    ];
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la suppression du personnel",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function change_personal_status(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'personal_id' => 'required|exists:personals,id',
                'status' => 'required|in:1,0',
                'secret_code' => 'required'
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
                $status = 400;
            } else {
                $auth_user = $this->get_auth_user($request);
                if ($auth_user !== null) {
                    $secret_code = Crypt::decryptString($auth_user->secret_code);
                    // dd($secret_code);
                    if ($secret_code !== $request->secret_code) {
                        $response = [
                            'success' => false,
                            'message' => "Code secret inccorrecte",
                            'result' => null
                        ];
                    } else {
                        $status_pers = intval($request->status);
                        $personal = Personal::where('id', $request->personal_id)->first();
                        if ($personal !== null) {
                            $personal->status = $status_pers;
                            $personal->save();
                            $user = $personal->user;
                            if ($user !== null) {
                                $user->status = $status_pers;
                                $user->save();
                            }
                            $response = [
                                'success' => true,
                                'message' => "Statut du personnel modifié avec succès",
                                'result' => $personal
                            ];
                        } else {
                            $response = [
                                'success' => false,
                                'message' => "Aucun personnel trouvé",
                                'result' => null,
                            ];
                        }
                    }
                } else {
                    $response = [
                        'success' => false,
                        'message' => "Utilisateur inconnue",
                        'result' => null
                    ];
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la modification du statut du personnel",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * getPersonalCode function
     * Retourner le code secret
     * @param Request $request
     * @return JsonResponse $response
     */
    public function getPersonalCode(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'secret_code' => 'required',
                'user_id' => 'required|exists:users,id'
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
                $status = 400;
            } else {
                $auth_user = $this->get_auth_user($request);
                if ($auth_user !== null) {
                    $secret_code = Crypt::decryptString($auth_user->secret_code);
                    // dd($secret_code);
                    if ($secret_code !== $request->secret_code) {
                        $response = [
                            'success' => false,
                            'message' => "Code secret inccorrecte",
                            'result' => null
                        ];
                    } else {
                        // $personal = Personal::where('user_id',$request->user_id)->first();
                        $user = User::where('id', $request->user_id)->first();
                        if ($user !== null) {
                            $code = Crypt::decryptString($user->secret_code);
                            $response = [
                                'success' => true,
                                'message' => "Code secret du personnel est: " . $code,
                                'result' => $code
                            ];
                        } else {
                            $response = [
                                'success' => false,
                                'message' => "Aucun personnel trouvé",
                                'result' => null,
                            ];
                        }
                    }
                } else {
                    $response = [
                        'success' => false,
                        'message' => "Utilisateur inconnue",
                        'result' => null
                    ];
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération du code secret",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }
}
