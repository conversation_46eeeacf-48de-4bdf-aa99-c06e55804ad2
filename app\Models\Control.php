<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Control extends Model
{
    use HasFactory;
    protected $fillable = [
        'supervisor_id','collector_id','client_id','date_control','status','subscription_id',
        'description','date_start_control','date_end_control','reason','agency_id','payments',
        'dettes','total_dette','total_payment'
    ];

    protected $casts = [
        'payments' => 'array',
        'dettes' => 'array',
    ];

    public function supervisor()
    {
        return $this->belongsTo(Personal::class, 'supervisor_id');
    }

    public function collector()
    {
        return $this->belongsTo(Personal::class, 'collector_id');
    }

    public function client()
    {
        return $this->belongsTo(Client::class, 'client_id');
    }

    public function subscription()
    {
        return $this->belongsTo(Subscription::class, 'subscription_id');
    }

    public function agency(){
        return $this->belongsTo(Agency::class, 'agency_id');
    }

    // public function getPaymentsAttribute($value)
    // {
    //     return json_decode($value);
    // }

    // public function getDettesAttribute($value)
    // {
    //     return json_decode($value);
    // }


}
