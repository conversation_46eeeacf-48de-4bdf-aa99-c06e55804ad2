<?php

use App\Models\Personal;
use App\Models\User;
use App\Services\EncryptionService;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Validator;



Route::group(['namespace' => "API"], function () {
    //include routes
    include_once __DIR__ . '/apis/auth.php';
    include_once __DIR__ . '/apis/root.php';
    include_once __DIR__ . '/apis/admin.php';
    // include_once __DIR__ . '/apis/agency.php';
    include_once __DIR__ . '/apis/collector.php';
    include_once __DIR__ . '/apis/caisse.php';
    include_once __DIR__ . '/apis/supervisor.php';
    // include_once __DIR__ . '/apis/helpers.php';

    Route::group(['namespace'=>"helpers",'prefix'=>'common'],function(){
        Route::get('/countries','CommonController@get_all_countries');
        Route::get('/cities','CommonController@get_all_cities');
        Route::get('/cities/quarters','CommonController@get_city_quarters');
        Route::get('/quarters','CommonController@get_all_quarters');
        Route::get('/app-configuration', 'CommonController@get_app_configuration');
    });
});

Route::get('/day', function () {
    $date = Carbon::now()->addDays(3)->format('Y-m-d H:i');
    $wallet_code = rand(1000, 9999) . "" . rand(1000, 9999) . "" . rand(1000, 9999) . "" . rand(1000, 9999);
    $start_at = Carbon::now()->addDays(-5)->addDay()->format('Y-m-d');
    $date_cotise = Carbon::now()->format('Y-m-d');
    $date_diff = Carbon::parse($date_cotise)->diffInDays(Carbon::parse($start_at));
    $payments_date = ['2023-08-06'];
    $arrieres = [];
    for ($i = 0; $i < $date_diff; $i++) {
        if (!in_array(Carbon::parse($start_at)->addDays($i)->format('Y-m-d'), $payments_date)) {
            array_push($arrieres, Carbon::parse($start_at)->addDays($i)->format('Y-m-d'));
            # code...
        }
        # code...
    }

    $current_year = date('Y');
    $expired_at = Carbon::now()->addMinutes(10)->format('Y-m-d H:i:s');
    return response()->json([
        'success' => true,
        'message' => 'Welcome to the API of the application',
        'result' => [
            'date' => $date,
            'wallet_code' => $wallet_code,
            'start_at' => $start_at,
            'date_cotise' => $date_cotise,
            'date_diff' => $date_diff,
            'arrieres' => $arrieres,
            'expired_at' => $expired_at,
            'current_year' => $current_year,
        ]
    ]);
});

Route::post('crypt', function (Request $request) {
    $response = [];
    $status = 200;
    try {
        $validator = Validator::make($request->all(), [
            'secret_code' => 'required|string'
        ]);
        if ($validator->fails()) {
            $status = 400;
            $response = [
                'success' => false,
                'message' => 'Erreur de validation',
                'errors' => $validator->errors(),
            ];
        }else {

            $secretCode = $request->secret_code;

            $crypt = Crypt::encryptString($secretCode);
            $decrypt = Crypt::decryptString($crypt);
            $response = [
                'success' => true,
                'message' => 'Crypt',
                'result' => [
                    'crypt' => $crypt,
                    'decrypt' => $decrypt
                ]
                ];
        }
    } catch (\Throwable $th) {
        $status = 500;
        $response = [
            'success' => false,
            'message' => $th->getMessage(),
            'result' => null,
            'errors' => null
        ];
    }

    return response()->json($response, $status);
});

Route::get('code', function () {
    $name = strtoupper("PC LENOVO");
    $code = 'PRD' . rand(1000, 9999);
    $shuffled_code = $code . str_shuffle(str_replace(' ', '', $name));
    $shuffled_code = htmlspecialchars($shuffled_code);
    $shuffled_code = str_shuffle($shuffled_code);

    $wallet_code = rand(1000, 9999) . " " . rand(1000, 9999) . " " . rand(1000, 9999) . " " . rand(1000, 9999);
    $wallet_code = str_replace(' ', '', $wallet_code);
    return str_shuffle($wallet_code);
});

Route::post('test-days', function (Request $request) {
    $dateDebut = $request->dateDebut;
    $joursAjout = $request->joursAjout;
    $date = new DateTime($dateDebut);

    while ($joursAjout > 0) {
        $date->modify('+1 day');

        if ($date->format('N') < 6) {  // Vérifie si c'est un jour de semaine (1 à 5)
            $joursAjout--;
        }
    }

    return $date->format('Y-m-d');
});

Route::get('test-cotisation-v2', function () {
    $metadata = [
        [
            "id"=> "c6a78c84-b8b4-40d4-a1c4-0cdce38bbfc4",
            "key"=> 1,
            "month"=> 1,
            "amount"=> 100,
            "payment_date"=> "2024-09-13 09:45:30",
            "status"=> "completed",
            "versement_id"=> null
        ],
        [
            "id"=> "214a2713-ef54-4369-b2b1-dc00a9021e7b",
            "key"=> 2,
            "month"=> 1,
            "amount"=> 100,
            "payment_date"=> "2024-09-13 09:45:30",
            "status"=> "completed",
            "versement_id"=> null
        ],
        [
            "id"=> "9193d208-c8bd-4bf1-a829-49655a79daa5",
            "key"=> 3,
            "month"=> 1,
            "amount"=> 100,
            "payment_date"=> "2024-09-13 09:45:30",
            "status"=> "completed",
            "versement_id"=> null
        ],
        [
            "id"=> "de7c5771-d54d-4a50-aae5-4847ea726ef4",
            "key"=> 4,
            "month"=> 1,
            "amount"=> 100,
            "payment_date"=> "2024-09-13 09:45:30",
            "status"=> "completed",
            "versement_id"=> null
        ],
        [
            "id"=> "651ab079-757e-4989-984c-a3260fc49e0f",
            "key"=> 5,
            "month"=> 1,
            "amount"=> 100,
            "payment_date"=> "2024-09-13 09:45:30",
            "status"=> "completed",
            "versement_id"=> null
        ],
        [
            "id"=> "84ef76a0-db2d-4b40-b0b2-271ed8eefaae",
            "key"=> 6,
            "month"=> 1,
            "amount"=> 100,
            "payment_date"=> "2024-09-13 09:47:22",
            "status"=> "completed",
            "versement_id"=> null
        ]
        ];
    $newPayments = [
        [
            "id" => "10fd2d04-7062-423c-8c28-4799b6055b82",
            "month" => 1,
            "key" => 1,
            "amount" => 100,
            "description" => "Cotisation du 2024-09-21 13:53:04",
            "payment_date" => "2024-09-21 13:53:04",
            "payment_mode" => "CASH",
            "status" => "completed",
            "versement_id" => null,
            "versement" => null
        ],
        [
            "id"=> "10fd2d04-7062-423c-8c28-4799b6055b82",
            "month" => 1,
            "key"=> 7,
            "amount"=> 100,
            "description"=> "Cotisation du 2024-09-21 13:53:04",
            "payment_date"=> "2024-09-21 13:53:04",
            "payment_mode"=> "CASH",
            "status"=> "completed",
            "versement_id"=> null,
            "versement"=> null
        ],
        [
            "id"=> "521b9ab9-1484-4008-a1f7-ea494d62a27a",
            "month" => 1,
            "key"=> 8,
            "amount"=> 100,
            "description"=> "Cotisation du 2024-09-21 13:53:04",
            "payment_date"=> "2024-09-21 13:53:04",
            "payment_mode"=> "CASH",
            "status"=> "completed",
            "versement_id"=> null,
            "versement"=> null
        ]
    ];
     // Extraire toutes les valeurs de 'key' dans $metadata
    $metadataKeys = array_column($metadata, 'key');

     // Filtrer les éléments de $newPayments dont la 'key' ne figure pas dans $metadataKeys
    $filteredNewPayments = array_filter($newPayments, function ($payment) use ($metadataKeys) {
        return !in_array($payment['key'], $metadataKeys);
    });

     // Afficher les résultats filtrés
    // Réindexer pour obtenir un tableau d'objets au lieu de tableau associatif
    $filteredNewPayments = array_values($filteredNewPayments);
    $metadata = array_merge($metadata, $filteredNewPayments);
    // Calculer le montant total des nouveaux paiements filtrés
    $totalAmountDiffPayments = array_sum(array_column($filteredNewPayments, 'amount'));

    return [
        "metadata" => $metadata,
        "totalAmountDiffPayments" => $totalAmountDiffPayments
    ];

});

Route::get('/test-encryption', function (Request $request) {
    $key = "FRIKALAB-GROUP";
    $encryptionService = new EncryptionService($key);
    $data = $request->input('data');
    $encrypted = $encryptionService->encrypt($data);
    $decrypted = $encryptionService->decrypt($encrypted);
    return [
        "data" => $data,
        "encrypted" => $encrypted,
        "decrypted" => $decrypted
    ];
});

Route::post('set-password', function (Request $request) {
    $userId = $request->user_id;
    $user = User::find($userId);
    if ($user == null) {
        return response()->json([
            'success' => false,
            'message' => 'Utilisateur non trouvé',
            'result' => null
        ]);
    }
    $user->password = Hash::make($request->password);
    $user->save();
    return response()->json([
        'success' => true,
        'message' => 'Mot de passe modifié avec succès',
        'result' => $user
    ]);
});





