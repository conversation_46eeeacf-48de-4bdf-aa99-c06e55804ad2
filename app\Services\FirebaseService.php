<?php

namespace App\Services;

use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\MulticastSendReport;

class FirebaseService
{
    protected $messaging;

    public function __construct() {
        $servceAccountPath = storage_path('benilife-b048a-firebase-adminsdk-msdd1-0c6503f658.json');

        $factory = (new Factory)->withServiceAccount($servceAccountPath);

        $this->messaging = $factory->createMessaging();
    }

    public function sendNotification($token, $title, $body, $data = []) {
        $message = CloudMessage::withTarget('token', $token)
            ->withNotification([
                'title' => $title,
                'body' => $body,
            ])
            ->withData($data);

        $this->messaging->send($message);
    }

    public function sendNotifications(array $tokens, $title, $body, $data = []) {
        $message = CloudMessage::new()
            ->withNotification([
                'title' => $title,
                'body' => $body,
            ])
            ->withData($data);

        $report = $this->messaging->sendMulticast($message, $tokens);

        return $report;
    }
}
