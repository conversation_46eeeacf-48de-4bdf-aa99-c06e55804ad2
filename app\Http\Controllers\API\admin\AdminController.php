<?php

namespace App\Http\Controllers\API\admin;

use App\Http\Controllers\API\helpers\HelperController;
use App\Models\Agency;
use App\Models\City;
use App\Models\Client;
use App\Models\Cotisation;
use App\Models\Country;
use App\Models\Pack;
use App\Models\Payment;
use App\Models\Quarter;
use App\Models\Subscription;
use App\Models\Versement;
use App\Models\Wallet;
use App\Models\Personal;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class AdminController extends HelperController
{
    /**
     * get_all_agencies function
     * Get all agencies
     * @param Request $request
     * @return JsonResponse
     */
    public function get_all_agencies(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $page = $request->input('page', 1);
            $perPage = $request->input('limit', 15);
            $agencies = Agency::orderBy('id', 'desc')->with(['city', 'quarter', 'responsable', 'parent'])
                ->withCount(['clients', 'subscriptions', 'personals'])
                ->paginate($perPage, ['*'], 'page', $page);

            $response = [
                'success' => true,
                'message' => "Liste des agences",
                'result' => $agencies
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des agences",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * get_agency function
     * Get agency by id
     * @param Request $request
     * @return JsonResponse
     */
    public function get_detail_agency(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'agency_id' => 'required|exists:agencies,id'
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
            } else {
                // $agency = $this->get_agency($request->agency_id);
                $data = Agency::where('id', $request->agency_id)->with([
                    'city',
                    'quarter',
                    'clients',
                    'subscriptions',
                    'responsable',
                    'parent',
                    'annexes',
                    'wallet',
                    'transactions',
                    'collectors',
                    'currentCollectors',
                    'supervisors',
                    'cashiers',
                    'currentSupervisors',
                    'currentCashiers',
                    'controls',
                    'personals' => function ($query) {
                        $query->with(['role', 'city', 'quarter', 'currentAgency']);
                    },
                    'clients' => function ($query) {
                        $query->with(['collectors', 'city', 'quarter', 'agency'])
                            ->withSum('cotisations', 'total_amount')
                            // ->orderBy('cotisations_total_amount', 'desc')
                            ->orderBy('created_at', 'desc');
                    },
                    'collectors' => function ($query) {
                        $query->with([
                            'currentAgency',
                            'user',
                            'user',
                            'role',
                            'city',
                            'quarter',
                            'versements',
                            'cotisations',
                            'payments',
                            'clients',
                            'subscriptions',
                        ]);
                    },
                    'currentCollectors' => function ($query) {
                        $query->with([
                            'currentAgency',
                            'user',
                            'user',
                            'role',
                            'city',
                            'quarter',
                            'versements',
                            'cotisations',
                            'payments',
                            'clients',
                            'subscriptions',
                        ]);
                    },
                    'supervisors' => function ($query) {
                        $query->with([
                            'currentAgency',
                            'user',
                            'user',
                            'role',
                            'city',
                            'quarter',
                        ]);
                    },
                    'currentSupervisors' => function ($query) {
                        $query->with([
                            'currentAgency',
                            'user',
                            'user',
                            'role',
                            'city',
                            'quarter',
                        ]);
                    },
                    'cashiers' => function ($query) {
                        $query->with([
                            'currentAgency',
                            'user',
                            'user',
                            'role',
                            'city',
                            'quarter',
                            'versements'
                        ]);
                    },
                    'currentCashiers' => function ($query) {
                        $query->with([
                            'currentAgency',
                            'user',
                            'user',
                            'role',
                            'city',
                            'quarter',
                            'versements'
                        ]);
                    },
                    'city' => function ($query) {
                        $query->with(['country']);
                    },
                    'versements' => function ($query) {
                        $query->with(['collector', 'cashier'])->orderBy('created_at', 'desc');
                    },
                    'cotisations' => function ($query) {
                        $query->with(['collector', 'client', 'payments', 'agency', 'payments',])->orderBy('created_at', 'desc');
                    },
                    'payments' => function ($query) {
                        $query->with(['collector', 'client'])->orderBy('created_at', 'desc');
                    },
                    'subscriptions' => function ($query) {
                        $query->with(['client', 'pack', 'cotisation', 'collector', 'agency'])->orderBy('created_at', 'desc');
                    },
                ])
                    ->withSum('versements', 'amount')
                    ->withSum('cotisations', 'total_amount')
                    ->withSum('payments', 'amount')
                    ->withSum('subscriptions', 'price')
                    ->first();
                $response = [
                    'success' => true,
                    'message' => "Détail de l'agence",
                    'result' => $data
                ];
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération de l'agence",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * create_agency function
     * Create new agency
     * @param Request $request
     * @return void
     */
    public function create_agency(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'name' => 'required',
                'city_id' => 'required|exists:cities,id',
                'quarter_id' => 'required|exists:quarters,id',
                'localisation' => 'nullable',
                'perimeters' => 'nullable',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
            } else {
                $check_agency_exist = Agency::where('name', $request->name)->first();
                if ($check_agency_exist != null) {
                    $response = [
                        'success' => false,
                        'message' => "Cette agence existe déjà",
                        'result' => null,
                        'errors' => null
                    ];
                } else {
                    $responsable = $request->responsable;
                    $wallet_code = rand(1000, 9999) . " " . rand(1000, 9999) . " " . rand(1000, 9999) . " " . rand(1000, 9999);
                    $wallet_code = str_replace(' ', '', $wallet_code);
                    $code = $this->generate_code($request->name);
                    $perimeters = $request->perimeters;

                    $agency = new Agency();
                    $agency->name = $request->name;
                    $agency->phone = $request->phone;
                    $agency->email = $request->email;
                    $agency->code = $code;
                    $agency->status = $request->status ?? "active";
                    $agency->responsable_id = $responsable;
                    $agency->address = $request->address;
                    $agency->localisation = json_encode($request->localisation);
                    $agency->city_id = $request->city_id;
                    $agency->quarter_id = $request->quarter_id;
                    $agency->perimeters = $perimeters;
                    $agency->save();
                    if ($agency == null) {
                        # error agency create...
                        $response = [
                            'success' => false,
                            'message' => "Echec de la création de l'agence",
                            'result' => null,
                            'errors' => null
                        ];
                        $status = 500;
                    } else {
                        # code...
                        $wallet = Wallet::create([
                            'code' => str_shuffle($wallet_code),
                            'balance' => 0,
                            'agency_id' => $agency->id,
                            'status' => 'active',
                            'type' => 'agency'
                        ]);
                        $response = [
                            'success' => true,
                            'message' => "Agence créée avec succès",
                            'result' => [
                                'agency' => $agency,
                                'wallet' => $wallet
                            ],
                            'errors' => null,
                        ];
                    }
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la création de l'agence",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * add_agency_responsable function
     * Add or update agency responsable
     * @param Request $request
     * @return void
     */
    public function add_agency_responsable(Request $request)
    {
        $response = [];
        $status = [];
        try {
            $validate = Validator::make($request->all(), [
                'agency_id' => 'required|exists:agencies,id',
                'responsable_id' => 'required|numeric|exists:personals,id',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
            } else {
                $agency = $this->get_agency($request->agency_id);
                $responsable = $request->responsable;
                $message = ($agency->responsable_id == null) ? "Responsable ajouté avec succès" : "Responsable modifié avec succès";
                $agency->responsable_id = $responsable;
                $agency->save();
                $response = [
                    'success' => true,
                    'message' => $message,
                    'result' => $agency,
                    'errors' => null
                ];
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de l'ajout du responsable",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function update_agency(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'agency_id' => 'required|exists:agencies,id'
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->errors(),
                ];
            } else {
                $agency = $this->get_agency($request->agency_id);
                if ($agency !== null) {
                    $agency->name = $request->name ?? $agency->name;
                    $agency->phone = $request->phone ?? $agency->phone;
                    $agency->email = $request->email ?? $agency->email;
                    $agency->code = $request->code !== null ? $request->code : $agency->code;
                    $agency->status = $request->status ?? $agency->status;
                    $agency->responsable_id = $request->responsable_id ?? $agency->responsable_id;
                    $agency->address = $request->address !== null ? $request->address : $agency->address;
                    $agency->localisation = $request->localisation ?? $agency->localisation;
                    $agency->city_id = $request->city_id !== null ? $request->city_id : $agency->city_id;
                    $agency->quarter_id = $request->quarter_id !== null ? $request->quarter_id : $agency->quarter_id;
                    $agency->perimeters = $request->perimeters ?? $agency->perimeters;

                    $agency->save();

                    $response = [
                        'success' => true,
                        'message' => "Agence modifiée avec succès",
                        'result' => $agency,
                    ];
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la modification de l'agence",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function getCountries(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $countries = Country::orderBy('id', 'desc')->with(['cities', 'agencies'])->get();
            $response = [
                'success' => true,
                'message' => "Liste des pays",
                'result' => $countries
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des pays",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function getCities(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $countryId = $request->input('country_id', null);
            $query = City::orderBy('id', 'desc');
            if ($countryId != null) {
                $query->where('country_id', $countryId);
            }
            $cities = $query->with(['country'])->withCount(['clients', 'agencies', 'personals', 'quarters'])->get();

            $response = [
                'success' => true,
                'message' => "Liste des villes",
                'result' => $cities
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des villes",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function getQuarters(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $cityId = $request->input('city_id', null);
            $query = Quarter::orderBy('id', 'desc');
            if ($cityId != null) {
                $query->where('city_id', $cityId);
            }
            $quarters = $query->with(['city'])->withCount(['clients', 'personals', 'agencies'])->get();

            $response = [
                'success' => true,
                'message' => "Liste des quartiers",
                'result' => $quarters
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des quartiers",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function add_country(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'name' => 'required',
                'prefix' => 'nullable',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
            } else {
                $country = Country::create([
                    'name' => $request->name,
                    'prefix' => $request->prefix
                ]);
                if ($country !== null) {
                    $response = [
                        'success' => true,
                        'message' => "Pays créé avec succès",
                        'result' => $country,
                        'errors' => null
                    ];
                } else {
                    $response = [
                        'success' => false,
                        'message' => "Echec de la création du pays",
                        'result' => null,
                        'errors' => null
                    ];
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la création du pays",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function add_cities(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'cities' => 'required|array',
                'cities.*.name' => 'required|unique:cities,name',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
            } else {
                $data_c = $request->cities;
                $result = [];
                foreach ($data_c as $key => $value) {
                    if (isset($value['name']) && isset($value['country_id'])) {
                        $result[] = City::create([
                            'name' => $value['name'],
                            'address' => $value['address'],
                            'country_id' => $value['country_id']
                        ]);
                    }
                }
                $response = [
                    'success' => true,
                    'message' => "Villes créées avec succès",
                    'result' => $result,
                ];
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la création des villes",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * add_quarters function
     *  Add or update quartiers
     * @param Request $request
     * @return void
     */
    public function add_quarters(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'quarters' => 'required|array',
                'quarters.*.name' => 'required|unique:quarters,name',
                'city_id' => 'required|exists:cities,id',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
            } else {
                $city = City::where('id', $request->city_id)->first();
                if ($city !== null) {
                    $data_c = $request->quarters;
                    $result = [];
                    foreach ($data_c as $key => $value) {
                        if (isset($value['name'])) {
                            $result[] = $city->quarters()->create([
                                'name' => $value['name'],
                                'description' => $value['description'],
                            ]);
                        }
                    }
                    $response = [
                        'success' => true,
                        'message' => "Quartiers créés avec succès",
                        'result' => $result,
                    ];
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la création des quartiers",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * dashboard function
     *  Get dashboard
     * @param Request $request
     * @return JsonResponse $response
     */
    public function dashboard(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $data = [];
            $clients = Client::orderBy('created_at', 'desc')->with([
                'user',
                'agency',
                'quarter',
                'city',
                'collectors',
                'subscriptions',
                'cotisations',
                'cotisations' => function ($query) {
                    $query->sum('total_amount');
                },
                'collector' => function ($query) {},
            ])->withSum('cotisations', 'total_amount')->get();

            $subscriptions = Subscription::orderBy('created_at', 'desc')->with([
                'pack',
                'client',
                'collector',
            ])->get();

            $subs_by_day = Subscription::orderBy('created_at', 'desc')->whereDate('created_at', Carbon::now()->format('Y-m-d'))
                ->with(['pack', 'client', 'collector',])
                ->get();

            $cotisations = Cotisation::orderBy('created_at', 'desc')->get();
            $versements = Versement::orderBy('created_at', 'desc')->with([
                'collector',
                'agency',
                'cashier'
            ])->get();
            $agencies = Agency::orderBy('created_at', 'asc')->with([
                'wallet'
            ])
                ->select('*')
                ->withSum('wallet', 'balance')
                ->withSum('cotisations', 'total_amount')
                ->withSum('subscriptions', 'price')
                ->get();
            $total_cotisations = Cotisation::orderBy('created_at', 'desc')->sum('total_amount');
            $total_subscriptions = Subscription::orderBy('created_at', 'desc')->sum('price');
            $total_versements_day = Versement::orderBy('created_at', 'desc')->whereDate('created_at', Carbon::now()->format('Y-m-d'))->sum('amount');
            $total_versements = Versement::orderBy('created_at', 'desc')->sum('amount');
            $payments = Payment::whereNotNull('cotisation_id')->orderBy('created_at', 'desc')->with([
                'cotisation',
                'client',
                'collector',
                'agency'
            ])->get();

            $gobal_collect_sum_day = $this->get_global_collecte_sum_waiting();

            $cotisations_by_day  = Payment::whereNotNull('cotisation_id')->orderBy('created_at', 'desc')->with([
                'cotisation',
                'client',
                'collector',
                'agency'
            ])->whereDate('payment_date', Carbon::now()->format('Y-m-d'))->get();

            $carnets_inactif = $this->get_carnets_inactif();

            $data = [

                'total_cotisations' => $total_cotisations,
                'total_subscriptions' => $total_subscriptions,
                'total_versements_day' => $total_versements_day,
                'total_versements' => $total_versements,
                'payments' => $payments,
                'gobal_collect_sum_day' => $gobal_collect_sum_day,
                'subs_by_day' => $subs_by_day,
                'cotisations_by_day' => $cotisations_by_day,
                'carnets_inactif' => $carnets_inactif,
            ];

            $response = [
                'success' => true,
                'message' => "Dashboard",
                'result' => $data
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération du dashboard",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function getTotalCollectByDay()
    {
        // set_time_limit(10000);
        // ini_set('memory_limit', '4048M');
        // ini_set('output_buffering', '4096');
        try {
            Log::info("Montant total collecté du jour : ");
            $payment_date = Carbon::parse("2021-09-27"); // Remplacer par Carbon::today() pour la date actuelle
            $total_collected = 0;

            // Récupérer les paiements correspondants
            $total_collect_by_day = Payment::whereNotNull('cotisation_id')
                ->where('payment_type', 'COLLECT')
                ->whereNull('versement_id')
                ->get();

            // dd($total_collect_by_day);

            foreach ($total_collect_by_day as $collect) {
                // Décoder le champ metadata JSON
                $metadata = $collect->metadata;

                // Vérifier que le décodage est réussi et que metadata est bien un tableau
                if (json_last_error() === JSON_ERROR_NONE && is_array($metadata)) {
                    // Filtrer pour obtenir les paiements du jour
                    $today_metadata = array_filter($metadata, function ($meta) use ($payment_date) {
                        return isset($meta['payment_date']) &&
                            Carbon::parse($meta['payment_date'])->format('Y-m-d') == $payment_date->format('Y-m-d');
                    });

                    // Ajouter le montant du jour
                    $total_collected += array_sum(array_column($today_metadata, 'amount'));
                } else {
                    Log::warning('Erreur de décodage JSON ou metadata invalide pour le paiement ID : ' . $collect->id);
                }
            }

            Log::info("Montant total collecté du jour : " . $total_collected);
            return $total_collected;
        } catch (\Throwable $th) {
            // Loguer l'erreur avant de la remonter
            Log::error("Erreur lors de la récupération du montant total collecté du jour : " . $th->getMessage());
            throw $th;
        }
    }

    public function dashboardAnalitycs(Request $request)
    {
        $response = [];
        $status = 200;

        try {
            // Requête unifiée pour les statistiques des souscriptions
            $subscriptionStats = Subscription::selectRaw('
            COUNT(*) as total_subscriptions,
            SUM(carnet_price) as total_subscriptions_amount,
            SUM(CASE WHEN status = "finished" THEN 1 ELSE 0 END) as total_subscriptions_finished,
            SUM(CASE WHEN status = "delivered" THEN 1 ELSE 0 END) as total_subscriptions_delivered,
            SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as subs_by_day_count
        ')->first();

            // Requête unifiée pour les statistiques des cotisations
            $cotisationStats = Cotisation::selectRaw('
            SUM(total_amount) as total_cotisations_amount,
            SUM(CASE WHEN status = "finished" THEN total_amount ELSE 0 END) as total_cotisation_amount_completed,
            SUM(CASE WHEN DATE(created_at) = CURDATE() THEN total_amount ELSE 0 END) as cotisations_by_day_amount
        ')->first();

            // Requête pour les versements
            $versementStats = Versement::selectRaw('
            SUM(amount) as total_versements_amount,
            SUM(CASE WHEN status = "confirmed" AND DATE(created_at) = CURDATE() THEN amount ELSE 0 END) as total_versements_day
        ')->first();

            // Autres statistiques
            $total_clients = Client::count();
            $total_carnets = Pack::count();
            $total_agencies = Agency::count();
            $gobal_collect_sum_day = $this->get_global_collecte_sum_waiting();

            // Formatage des données pour correspondre à l'interface Analytical
            $data = [
                'total_clients' => (int)$total_clients,
                'total_carnets' => (int)$total_carnets,
                'total_subscriptions_finished' => (int)$subscriptionStats->total_subscriptions_finished,
                'total_subscriptions_delivered' => (int)$subscriptionStats->total_subscriptions_delivered,
                'total_subscriptions' => (int)$subscriptionStats->total_subscriptions,
                'total_subscriptions_amount' => (float)$subscriptionStats->total_subscriptions_amount,
                'total_cotisations_amount' => (float)$cotisationStats->total_cotisations_amount,
                'total_cotisation_amount_completed' => (float)$cotisationStats->total_cotisation_amount_completed,
                'total_versements_amount' => (string)$versementStats->total_versements_amount, // Format string comme demandé
                'subs_by_day_count' => (int)$subscriptionStats->subs_by_day_count,
                'total_versements_day' => (float)$versementStats->total_versements_day,
                'cotisations_by_day_amount' => (float)$cotisationStats->cotisations_by_day_amount,
                'total_agencies' => (int)$total_agencies,
                'gobal_collect_sum_day' => (float)$gobal_collect_sum_day
            ];

            $response = [
                'success' => true,
                'message' => "Dashboard analytics retrieved successfully",
                'result' => $data
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Failed to retrieve dashboard analytics",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }

        return $this->apiResponse($response, $status);
    }


    public function getCarnetInactifs(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $carnets_inactif = $this->get_carnets_inactif();
            $response = [
                'success' => true,
                'message' => "Récupération des carnets inactifs",
                'result' => $carnets_inactif,
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des carnets inactifs",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function getPaymentByPeriod(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            // Déterminer la période (jour ou mois)
            $period = $request->input('period', 'day'); // Par défaut 'day'

            // Filtrer les paiements en fonction de la période choisie
            if ($period === 'month') {
                $startOfMonth = Carbon::now()->startOfMonth();
                $endOfMonth = Carbon::now()->endOfMonth();
                $payments = Payment::whereNotNull('cotisation_id')
                    ->whereBetween('payment_date', [$startOfMonth, $endOfMonth])
                    ->orderBy('payment_date', 'desc')
                    ->with(['cotisation', 'client', 'collector', 'pack'])
                    ->get();
            } else {
                $today = Carbon::now()->format('Y-m-d');
                $payments = Payment::whereNotNull('cotisation_id')
                    ->whereDate('payment_date', $today)
                    ->orderBy('payment_date', 'desc')
                    ->with(['cotisation', 'client', 'collector', 'pack'])
                    ->get();
            }

            // Grouper les paiements par client, pack_id et date de paiement, et calculer la somme des paiements
            $groupedPayments = $payments->groupBy(function ($payment) {
                return $payment->client_id . '-' . $payment->cotisation->pack_id . '-' . Carbon::parse($payment->payment_date)->format('Y-m-d');
            })->map(function ($group) {
                $totalAmount = $group->sum('amount');
                $client = $group->first()->client;
                $pack = $group->first()->pack;
                $collector = $group->first()->collector;
                $cotisation = $group->first()->cotisation;
                $paymentDate = $group->first()->payment_date;
                $totalPaymentKey = $group->first()->client_id . '-' . $group->first()->pack_id . '-' . Carbon::parse($group->first()->payment_date)->format('Y-m-d');
                $keys = $group->count();
                $tarif = $group->first()->amount;

                return [
                    'client' => $client,
                    'pack' => $pack,
                    'payment_date' => $paymentDate,
                    'total_amount' => $totalAmount,
                    'collector' => $collector,
                    'cotisation' => $cotisation,
                    'payment_key' => $totalPaymentKey,
                    'keys' => $keys,
                    'tarif' => $tarif,
                ];
            })->values(); // Utiliser values() pour réindexer la collection

            $response = [
                'success' => true,
                'message' => "Récupération des paiements par " . ($period === 'month' ? "mois" : "jour"),
                'result' => $groupedPayments,
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des paiements",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function getSubscriptionsByPeriod(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            // Déterminer la période (jour ou mois)
            $period = $request->input('period', 'day'); // Par défaut 'day'
            $from = $request->input('from');
            $to = $request->input('to');
            $today = Carbon::now()->format('Y-m-d'); // Pour le jour
            $startOfMonth = Carbon::now()->startOfMonth(); // Pour le mois
            $endOfMonth = Carbon::now()->endOfMonth(); // Pour le mois

            $page = $request->input('page', 1);
            $perPage = $request->input('perPage', 50);

            // Définir la requête de base
            $query = Subscription::with(['client', 'pack', 'collector'])->orderBy('created_at', 'desc');

            // Appliquer les filtres selon la période
            if ($from && $to) {
                $subscriptions = $query->whereBetween('created_at', [$from, $to])->paginate($perPage, ['*'], 'page', $page); // Pagination ajoutée
            } elseif ($period === 'month') {
                $subscriptions = $query->whereBetween('created_at', [$startOfMonth, $endOfMonth])->paginate($perPage, ['*'], 'page', $page); // Pagination ajoutée
            } else {
                $subscriptions = $query->whereDate('created_at', $today)->paginate($perPage, ['*'], 'page', $page); // Pagination ajoutée
            }

            $response = [
                'success' => true,
                'message' => "Récupération des abonnements par " . ($period === 'month' ? "mois" : "jour"),
                'result' => $subscriptions,
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des abonnements",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function getVersementByPeriod(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $period = $request->input('period', 'day'); // Par défaut 'day'
            $today = Carbon::now()->format('Y-m-d');
            if ($period === 'month') {
                $startOfMonth = Carbon::now()->startOfMonth();
                $endOfMonth = Carbon::now()->endOfMonth();
                $versements = Versement::where('created_at', '>=', $startOfMonth)
                    ->where('created_at', '<=', $endOfMonth)
                    ->where('status', 'confirmed')
                    ->orderBy('created_at', 'desc')
                    ->with(['collector', 'agency', 'cashier'])
                    ->get();
            } else {
                $versements = Versement::whereDate('created_at', $today)
                    ->orderBy('created_at', 'desc')
                    ->with(['collector', 'agency', 'cashier'])
                    ->get();
            }
            $response = [
                'success' => true,
                'message' => "Récupération des versements par " . ($period === 'month' ? "mois" : "jour"),
                'result' => $versements,
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des abonnements",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function getLastClientByPeriod(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $period = $request->input('period', 'day'); // Par défaut 'day'
            $today = Carbon::now()->format('Y-m-d');
            if ($period === 'month') {
                $startOfMonth = Carbon::now()->startOfMonth();
                $endOfMonth = Carbon::now()->endOfMonth();
                $clients = Client::where('created_at', '>=', $startOfMonth)
                    ->where('created_at', '<=', $endOfMonth)
                    ->orderBy('created_at', 'desc')
                    ->with(['user', 'agency', 'quarter', 'city'])
                    ->get();
            } else {
                $clients = Client::whereDate('created_at', $today)
                    ->orderBy('created_at', 'desc')
                    ->with(['user', 'agency', 'quarter', 'city'])
                    ->get();
            }
            $response = [
                'success' => true,
                'message' => "Récupération des clients par " . ($period === 'month' ? "mois" : "jour"),
                'result' => $clients,
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des clients",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function getCollectorsRankingByDateRange(Request $request)
    {
        $response = [];
        $status = 200;

        try {
            // Récupération de la date de début et de fin de la période
            $startDate = Carbon::parse($request->query('start_date', Carbon::now()->format('Y-m-d')))->startOfDay();
            $endDate = $request->query('end_date')
                ? Carbon::parse($request->query('end_date'))->endOfDay()
                : $startDate->copy()->endOfDay(); // Si end_date non fournie, prend la même journée

            // Récupération et classement des collecteurs ayant des paiements dans la période spécifiée
            $collectors = Personal::with(['payments' => function ($query) use ($startDate, $endDate) {
                $query->whereBetween('updated_at', [$startDate, $endDate])
                    ->where(function ($q) {
                        $q->where('payment_type', 'COLLECT')
                            ->orWhere('payment_type', 'CARNET');
                    });
            }])
                ->get()
                ->map(function ($collector) {
                    // Calcul du chiffre d'affaires des carnets vendus sur la période
                    $totalSales = $collector->payments
                        ->where('payment_type', 'CARNET')
                        ->sum('amount');

                    // Calcul du total des collectes sur la période
                    $totalCollected = $collector->payments
                        ->where('payment_type', 'COLLECT')
                        ->sum('amount');

                    // Total des activités (collectes + ventes de carnets)
                    $totalAmount = $totalSales + $totalCollected;

                    // Filtre pour inclure uniquement les collecteurs ayant une activité
                    return $totalAmount > 0 ? [
                        'collector_id' => $collector->id,
                        'name' => "{$collector->nom} {$collector->prenoms}",
                        'total_sales' => $totalSales,
                        'total_collected' => $totalCollected,
                        'total_amount' => $totalAmount,
                    ] : null; // Retourne null pour les collecteurs sans activité
                })
                ->filter() // Supprime les collecteurs sans activité (totalAmount = 0)
                ->sortByDesc('total_amount') // Classement par montant total des activités
                ->values() // Réindexe la collection pour un résultat plus propre
                ->all();

            // Construction de la réponse
            $response = [
                'success' => true,
                'message' => "Classement des collecteurs pour la période du $startDate au $endDate",
                'result' => $collectors,
            ];
        } catch (\Throwable $th) {
            // Gestion des erreurs
            $response = [
                'success' => false,
                'message' => "Échec de la récupération du classement des collecteurs",
                'result' => null,
                'errors' => $th->getMessage(),
            ];
            $status = 500;
        }

        return $this->apiResponse($response, $status);
    }
}
