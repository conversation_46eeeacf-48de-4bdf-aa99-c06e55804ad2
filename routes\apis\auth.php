<?php

use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes                                                 |
|--------------------------------------------------------------------------
|                                                    |
| Here is where you can register API routes for your app. |
|                                                    |                                                    |                                                    |
*/

Route::group(['namespace' => "auth", 'prefix' => 'auth'], function () {
    Route::post('/login', 'AuthController@login');
    Route::post('/register', 'AuthController@register');
    Route::post('/login-with-code', 'Auth<PERSON>ontroller@auth_with_secretCode');
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/logout', 'AuthController@logout');
        Route::get('/user', function (Request $request) {
            return $request->user();
        });
    });
});
