<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('personals', function (Blueprint $table) {
            $table->timestamp('date_nsce')->nullable()->after('phone');
            $table->string('lieu_nsce')->nullable()->after('date_nsce');
            $table->enum('gender', ['Masculin', 'Feminin'])->after('lieu_nsce')->nullable();
            $table->enum('situation_matrimoniale', ['Célibataire', 'Marié(e)', 'Divorcé(e)', 'Veuf(ve)', 'Concubin(e)'])->after('gender')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('personals', function (Blueprint $table) {
            $table->dropColumn('date_nsce');
            $table->dropColumn('lieu_nsce');
            $table->dropColumn('gender');
            $table->dropColumn('situation_matrimoniale');
        });
    }
};
