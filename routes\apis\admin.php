<?php

use Illuminate\Support\Facades\Route;


Route::group(['namespace' => 'admin', 'middleware' => ['auth:sanctum',]], function () {
    Route::prefix('admin')->group(function () {
        // Dashboard analitycs
        Route::prefix('analitycs')->group(function () {
            Route::get('/', 'AdminController@dashboardAnalitycs');
            Route::get('/activities-by-day','AdminController@getTotalCollectByDay');
            Route::get('/payments/{period?}', 'AdminController@getPaymentByPeriod');
            Route::get('/carnets-inactifs', 'AdminController@getCarnetInactifs');
            Route::get('/last-client/{period?}', 'AdminController@getLastClientByPeriod');
            Route::get('/subscriptions/{period?}', 'AdminController@getSubscriptionsByPeriod');
            Route::get('/versements/{period?}', 'AdminController@getVersementByPeriod');
            Route::get('/collectors-ranking', 'AdminController@getCollectorsRankingByDateRange');
            Route::get('/financial-health', 'FinancialController@getFinancialHealth');
            Route::get('/financial-trends', 'FinancialController@getFinancialTrends');
            Route::get('/top-collectors', 'AccountingController@topCollectors');
            Route::get('/top-clients', 'AccountingController@topClientsByRevenue');
            Route::get('/popular-packs', 'AccountingController@getPopularPacks');
        });
        // End dashboard analitycs

        Route::get('/dashboard', 'AdminController@dashboard');
        Route::get('/agencies', 'AdminController@get_all_agencies');
        Route::get('/agencies/details', 'AdminController@get_detail_agency');
        Route::post('/agencies/create', 'AdminController@create_agency');
        Route::post('/agencies/update', 'AdminController@update_agency');
        Route::post('/agencies/add_responsable', 'AdminController@add_agency_responsable');
        //GRH
        Route::get('/personals', 'PersonalController@get_all_personals');
        Route::get('/personals/role', 'PersonalController@get_personals_by_role');
        Route::post('/personals/add', 'PersonalController@addNewPersonal');
        Route::get('/personals/not_affected', 'PersonalController@get_personals_not_affected');
        Route::post('/personals/add-affectation', 'PersonalController@create_personals_affectation');
        Route::get('/personals/details', 'PersonalController@get_details_personal');
        Route::post('/personals/update', 'PersonalController@update_personal');
        Route::post('/personals/change_code', 'PersonalController@change_personal_secretCode');
        Route::post('/personals/delete', 'PersonalController@delete_personal');
        Route::post('/personals/change_status', 'PersonalController@change_personal_status');
        Route::post('/personals/secret_code', 'PersonalController@getPersonalCode');

        //CLIENTS
        Route::get('/clients', 'ClientController@getClients');
        Route::get('/clients/details', 'ClientController@get_client_details');
        Route::post('/clients/add', 'ClientController@add_client');
        Route::post('/clients/update', 'ClientController@update_client');
        // Route::post('/clients/delete','ClientController@delete_client');

        //USERS
        Route::get('/users', 'UserController@getUsers');
        Route::get('/roles', 'UserController@getRoles');
        Route::post('/users/add', 'UserController@addUser');
        Route::post('/users/change_password', 'UserController@changePassword');
        Route::post('/users/update', 'UserController@updateUser');
        Route::post('/users/disconnected', 'UserController@logoutUser');

        //PRODUCTS MANAGEMENT
        Route::get('/categories', 'ProductController@get_all_categories');
        Route::get('/products', 'ProductController@get_all_products');
        Route::get('/products/alerts', 'ProductController@get_alert_products');
        Route::get('/products/packs', 'ProductController@get_product_packs');
        Route::get('/products/clients-subscribe', 'ProductController@get_clients_subscribe_to_product');
        Route::get('/products/cotisations', 'ProductController@get_total_cotisation_by_product');
        Route::post('/products/add', 'ProductController@add_product');
        Route::post('/products/update', 'ProductController@update_product');
        Route::post('/categories/add', 'ProductController@addCategories');
        Route::post('/categories/update', 'ProductController@updateCategory');
        //PACK MANAGEMENT
        Route::get('/packs', 'PackController@get_all_packs');
        Route::get('/packs/products', 'PackController@get_pack_products');
        Route::get('/packs/subscriptions', 'PackController@get_pack_subscriptions');
        Route::get('/packs/details', 'PackController@get_details_pack');
        Route::post('/packs/add', 'PackController@add_pack');
        Route::post('/packs/update', 'PackController@update_pack');
        Route::post('/packs/update', 'PackController@update_pack');

        //COUNTRIES && CITIES && QUARTERS MANAGEMENT
        Route::get('/countries', 'AdminController@getCountries ');
        Route::get('/cities', 'AdminController@getCities');
        Route::get('/quarters', 'AdminController@getQuarters');
        Route::post('/cities/create', 'AdminController@add_cities');
        Route::post('/countries/create', 'AdminController@add_country');
        Route::post('/quarters/create', 'AdminController@add_quarters');

        //AGENCY ACCOUNTING MANAGEMENT
        Route::get('/accounting/cashiers', 'AccountingController@getCashiers');
        Route::get('/accounting/versements', 'AccountingController@get_versements');
        Route::get('/accounting/subscriptions', 'AccountingController@getSubscriptions');
        Route::get('/accounting/cotisations', 'AccountingController@getCotisations');
        Route::get('/accounting/payments', 'AccountingController@getPayments');
        Route::get('/accounting/transactions', 'AccountingController@get_transactions');
        Route::get('/accounting/controls', 'AccountingController@get_controls');
    });
});
