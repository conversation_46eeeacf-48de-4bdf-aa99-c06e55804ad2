<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Cotisation extends Model
{
    use HasFactory;
    protected $fillable = [
        'client_id','subscription_id','date_cotise','status','total_amount','description',
        'nbre_cotise','collector_id','agency_id','start_at','end_at', 'status', 'month', 'created_at', 'updated_at'
    ];

    public function client()
    {
        return $this->belongsTo(Client::class, 'client_id');
    }

    public function subscription()
    {
        return $this->belongsTo(Subscription::class, 'subscription_id');
    }

    public function collector()
    {
        return $this->belongsTo(Personal::class, 'collector_id');
    }

    /**
     * Get all of the payments for the Cotisation
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class, 'cotisation_id', 'id');
    }

    public function agency()
    {
        return $this->belongsTo(Agency::class, 'agency_id');
    }


}
