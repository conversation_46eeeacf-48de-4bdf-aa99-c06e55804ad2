Stack trace:
Frame         Function      Args
0007FFFFA120  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFA120, 0007FFFF9020) msys-2.0.dll+0x1FE8E
0007FFFFA120  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA3F8) msys-2.0.dll+0x67F9
0007FFFFA120  000210046832 (000210286019, 0007FFFF9FD8, 0007FFFFA120, 000000000000) msys-2.0.dll+0x6832
0007FFFFA120  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA120  000210068E24 (0007FFFFA130, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA400  00021006A225 (0007FFFFA130, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF51050000 ntdll.dll
7FFF4EE70000 KERNEL32.DLL
7FFF4E9D0000 KERNELBASE.dll
7FFF4FE90000 USER32.dll
7FFF4E790000 win32u.dll
7FFF4F320000 GDI32.dll
7FFF4E430000 gdi32full.dll
7FFF4E580000 msvcp_win.dll
7FFF4E830000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF50B00000 advapi32.dll
7FFF4F350000 msvcrt.dll
7FFF509D0000 sechost.dll
7FFF4E550000 bcrypt.dll
7FFF4F550000 RPCRT4.dll
7FFF4DC00000 CRYPTBASE.DLL
7FFF4E950000 bcryptPrimitives.dll
7FFF50D20000 IMM32.DLL
