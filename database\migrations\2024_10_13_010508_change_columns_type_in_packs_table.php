<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('packs', function (Blueprint $table) {
            $table->double('tarif', 10, 2)->comment('Mise du carnet')->change();
            $table->double('carnet_price', 10, 2)->default(200)->after('tarif')->comment('Prix de vente du carnet')->change();
            $table->double('total_price', 10, 2)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('packs', function (Blueprint $table) {
            //
        });
    }
};
