<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('packs', function (Blueprint $table) {
            // Change column type from string to decimal
            $table->decimal('tarif', 8, 2)->comment('Mise du carnet')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('packs', function (Blueprint $table) {
            // Revert the column type back to string
            $table->string('tarif')->comment('Mise du carnet')->change();
        });
    }
};
