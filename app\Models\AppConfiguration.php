<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class AppConfiguration extends Model
{
    use HasFactory;

    protected $fillable = [
        'app_type',
        'app_link',
        'ios_app_link',
        'app_version',
        'ios_app_version',
        'force_app_update',
        'app_maintenance',
        'file_config',
    ];

    protected $casts = [
        'file_config' => 'array',
    ];

    public function getFileConfigAttribute($value)
    {

        $fileConfig = json_decode($value, true);

        if (is_array($fileConfig)) {
            $fileConfig['file_url'] = url(Storage::url('apps/' . $fileConfig['file_name'].'.'.$fileConfig['file_extension']));
        }

        return $fileConfig;
    }
}
