<?php

namespace App\Http\Controllers\API\admin;

use App\Models\Role;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\API\helpers\HelperController;
use App\Models\Personal;

class UserController extends HelperController
{
    public function getUsers(Request $request){
        $response = [];
        $status = 200;
        try {
            $page = $request->input('page', 1);
            $perPage = $request->input('limit', 15);
            $users = User::orderBy('id','desc')->with(['role'])->paginate($perPage, ['*'], 'page', $page);
            $response = [
                'success'=>true,
                'message'=>"Liste des utilisateurs",
                'result'=>$users
            ];
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la récupération des utilisateurs",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * addUser function
     *  Ajouter un utilisateur
     * @param Request $request
     * @return JsonResponse $response
     */
    public function addUser(Request $request){
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(),[
                'username'=>'required',
                'email'=>'required|email|unique:users,email',
                'phone'=>'required|unique:users,phone',
                'password'=>'required',
                'role_id'=>'required|exists:roles,id',
                'password_confirm'=>'required|same:password'
            ]);
            if ($validate->fails()) {
                $response = [
                    'success'=>false,
                    'message'=>"Echec de validation des données",
                    'result'=>null,
                    'errors'=>$validate->getMessageBag()
                ];

            }else{
                $secret_code = rand(1000,9999);
                $user = User::create([
                    'username'=>$request->username,
                    'email'=>$request->email,
                    'phone'=>$request->phone,
                    'password'=>bcrypt($request->password),
                    'role_id'=>$request->role_id,
                    'secret_code'=>Crypt::encryptString($secret_code)
                ]);
                if ($user !== null) {
                    $response = [
                        'success'=>true,
                        'message'=>"Utilisateur ajouté avec succès",
                        'result'=>$user,
                        'errors'=>null
                    ];
                }else {
                    $response = [
                        'success'=>false,
                        'message'=>"Utilisateur non ajouté",
                        'result'=>null,
                        'errors'=>null,
                    ];
                }

            }
        } catch (\Throwable $th) {
            //throw $th;
        }
    }

    /**
     * getRoles function
     *  Récupérer les roles
     * @param Request $request
     * @return JsonResponse $response
     */
    public function getRoles(Request $request){
        $response = [];
        $status = 200;
        try {
            $roles = Role::orderBy('id','desc')->get();
            $response = [
                'success'=>true,
                'message'=>"Liste des roles",
                'result'=>$roles
            ];
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la récupération des roles",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * changePassword function
     *  Changer le mot de passe d'un utilisateur
     * @param Request $request
     * @return JsonResponse $response
     */
    public function changePassword(Request $request){
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(),[
                'user_id'=>'required|exists:users,id',
                'password'=>'required',
                'password_confirmation'=>'required|same:password',
                'secret_code'=>'required'
            ]);
            if ($validate->fails()) {
                $response = [
                    'success'=>false,
                    'message'=>"Echec de validation des données",
                    'result'=>null,
                    'errors'=>$validate->errors()
                ];
            } else {
                $user_auth = $request->user();
                $secret_code = Crypt::decryptString($user_auth->secret_code);
                // dd($secret_code);
                if ($secret_code !== $request->secret_code) {
                    $response = [
                        'success'=>false,
                        'message'=>"Code secret inccorrecte",
                        'result'=>null
                    ];
                }else {
                    $user = $this->get_user_by_id($request->user_id);
                    if ($user !== null) {
                        $user->password = Hash::make($request->password);
                        $user->save();
                        $response = [
                            'success'=>true,
                            'message'=>"Mot de passe modifié avec succès",
                            'result'=>$user,
                            'errors'=>null
                        ];
                    }
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la modification du mot de passe",
                'result'=>null,
                'except'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * updateUser function
     *  Modifier un utilisateur
     * @param Request $request
     * @return JsonResponse $response
     */
    public function updateUser(Request $request){
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(),[
                'user_id'=>'required|exists:users,id',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success'=>false,
                    'message'=>"Echec de validation des données",
                    'result'=>null,
                    'errors'=>$validate->getMessageBag(),
                ];
            }else{
                $user = $this->get_user_by_id($request->user_id);
                if ($user !== null) {
                    $phone = $request->phone;
                    $phone = str_replace('(+228)', '', $phone);
                    $phone = str_replace(' ', '', $phone);
                    $user->password = $user->password;
                    $user->update($request->all());
                    $personal = $user->personal;
                    if ($personal !== null) {
                        if ($personal->email !== $request->email || $personal->phone !== $request->phone) {
                            $personal->phone = $request->phone;
                            $personal->email = $request->email;
                            $personal->save();
                        }
                    }
                    $response = [
                        'success'=>true,
                        'message'=>"Utilisateur modifié avec succès",
                        'result'=>$user,
                        'errors'=>null
                    ];
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la modification des informations de l'utilisateur",
                'result'=>null,
                'except'=>$th->getMessage()
            ];
        }
        return $this->apiResponse($response,$status);
    }

    public function change_personal_secretCode(Request $request){
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(),[
                'user_id'=>'required',
                'secret_code'=>'required',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success'=>false,
                    'message'=>"Echec de validation des données",
                    'result'=>null,
                    'errors'=>$validate->getMessageBag()
                ];
            } else {
                $user = $this->get_auth_user($request);
                if ($user == null) {
                    $response = [
                        'success'=>false,
                        'message'=>"Utilisateur inconnue",
                        'result'=>null
                    ];
                } else {
                    $secret_code = Crypt::decryptString($user->secret_code);
                    // dd($secret_code);
                    if ($secret_code !== $request->secret_code) {
                        $response = [
                            'success'=>false,
                            'message'=>"Code secret inccorrecte",
                            'result'=>null
                        ];
                    }else{
                        $user = $this->get_user_by_id($request->user_id);
                        if ($user !== null) {
                            // dd($user_pers);
                            $secret_code = rand(1000,9999);
                            $user->secret_code = Crypt::encryptString($secret_code);
                            $user->save();
                            // $user->update([
                            //     'secret_code'=>Crypt::encrypt($secret_code)
                            // ]);
                            $response = [
                                'success'=>true,
                                'message'=>"Code secret modifié avec succès",
                                'result'=>[
                                    'user'=>$user,
                                    'secret_code'=>$secret_code
                                ]
                            ];

                        } else {
                            $response = [
                                'success'=>false,
                                'message'=>"Aucun personnel trouvé",
                                'result'=>null,
                            ];
                        }
                    }
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la modification du code secret",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * logoutUser function
     *  Deconnecter un utilisateur
     * @param Request $request
     * @return JsonResponse $response
     */
    public function logoutUser(Request $request){
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(),[
                'user_id'=>'required|exists:personals,id',
                'secret_code'=>'required',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success'=>false,
                    'message'=>"Echec de validation des données",
                    'result'=>null,
                    'errors'=>$validate->getMessageBag()
                ];
            }else{
                $user = $this->get_auth_user($request);
                if ($user == null) {
                    $response = [
                        'success'=>false,
                        'message'=>"Utilisateur inconnue",
                        'result'=>null
                    ];
                } else {
                    $secret_code = $request->secret_code;
                    // dd($secret_code);
                    if ($secret_code !== Crypt::decryptString($user->secret_code)) {
                        $response = [
                            'success'=>false,
                            'message'=>"Code secret incorrecte",
                            'result'=>null
                        ];
                    }else{
                        $personal = Personal::where('id', $request->user_id)->first();
                        $res_user = $this->get_user_by_id($personal->user_id);
                        if ($res_user !== null && $user->is_online == 1) {
                            $rm_token = $this->remove_old_user_auth($request->user_id);
                            if($rm_token){
                                $res_user->is_online = 0;
                                $res_user->save();
                                $response = [
                                    'success'=>true,
                                    'message'=>"Utilisateur déconnecté avec succès",
                                    'result'=>[
                                        'user'=>$res_user,
                                        'is_online'=>$res_user->is_online
                                    ],
                                    'errors'=>null
                                ];
                            }else {
                                $response = [
                                    'success'=>false,
                                    'message'=>"Echec de la déconnexion",
                                    'result'=>null,
                                    'except'=>[
                                        'user'=>$res_user,
                                        'is_online'=>$res_user->is_online
                                    ]
                                ];
                            }
                        }
                    }
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la modification du code secret",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }
}
