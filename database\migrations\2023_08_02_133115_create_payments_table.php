<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->float('amount');
            $table->timestamp('payment_date');
            $table->string('status')->default('pending');
            $table->integer('agency_id');
            $table->integer('collector_id');
            $table->integer('subscription_id')->nullable();
            $table->integer('cotisation_id')->nullable();
            $table->integer('pack_id')->nullable();
            $table->enum('payment_mode',['CASH','CHEQUE','VIREMENT','MOBILEMONEY'])->default('CASH');
            $table->text('description')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
