<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class AppConfigurationTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $app_configurations = [
            [
                
                'app_type' => 'COLLECTOR',
                'app_link' => null,
                'ios_app_link' => null,
                'app_version' => '1.0.0',
                'ios_app_version' => '1.0.0',
                'force_app_update' => false,
                'app_maintenance' => false,
                'file_config' => [
                    'id' => Str::uuid(),
                    'file_name' => 'app-release-1.0.2',
                    'file_thumbnail' => "",
                    'file_url' => "",
                    'file_type' => 'file',
                    'file_extension' => 'apk',
                ],

            ],
            [
                'app_type' => 'CASHIER',
                'app_link' => null,
                'ios_app_link' => null,
                'app_version' => '1.0.0',
                'ios_app_version' => '1.0.0',
                'force_app_update' => false,
                'app_maintenance' => false,
            ],
            [
                'app_type' => 'SUPERVISOR',
                'app_link' => null,
                'ios_app_link' => null,
                'app_version' => '1.0.0',
                'ios_app_version' => '1.0.0',
                'force_app_update' => false,
                'app_maintenance' => false,
            ],
            [
                'app_type' => 'DELIVERY',
                'app_link' => null,
                'ios_app_link' => null,
                'app_version' => '1.0.0',
                'ios_app_version' => '1.0.0',
                'force_app_update' => false,
                'app_maintenance' => false,
            ],
        ];

        foreach ($app_configurations as $app_configuration) {
            \App\Models\AppConfiguration::create($app_configuration);
        }
    }
}
