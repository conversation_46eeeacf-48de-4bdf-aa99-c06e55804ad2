<?php

namespace App\Http\Controllers\API\Traits;
use App\Models\Wallet;
use App\Models\Transaction;
use App\Models\User;

trait WalletTrait
{
    /**
     * wallet_debite function
     * Permet de débiter un wallet
     * @param Wallet $wallet
     * @param float $amount
     * @return array $result
     */
    public function wallet_debite(Wallet $wallet,float $amount, User $user = null){
        $response = [];
        try {
            if($wallet !== null){
                $solde = $wallet->balance;
                if($solde >= 0){
                    $new_solde = $solde >= $amount ? $solde-$amount : $amount - $solde;
                    $res = $wallet->update([
                        'balance'=>$new_solde,
                    ]);
                    if($res){
                        $transact = $wallet->transactions()->create([
                            'user_id'=>$user->id != null ? $user->id : request()->user()->id,
                            'agency_id' => $wallet->agency_id,
                            'amount'=>$amount,
                            'description'=>"Opération de débit effectué sur le compte {$wallet->code}",
                            'status'=>1,
                            'type'=>1,
                            'wallet_id'=>$wallet->id,
                            'date_transact'=>date('Y-m-d H:i:s'),
                        ]);
                        $response = [
                            'success'=>true,
                            'result'=>[
                                'solde'=>$new_solde,
                                'transaction'=>$transact,
                            ],
                            'message'=>"Wallet débité avec succès"
                        ];
                    }else{
                        $response = [
                            'success'=>false,
                            'result'=>null,
                            'message'=>"Echec lors du prélevement du compte"
                        ];
                    }
                }else{
                    $response = [
                        'success'=>false,
                        'result'=>null,
                        'message'=>"Solde insuffisant pour effectuer cette opération",
                    ];
                }
            }else{
                $response = [
                    'success'=>false,
                    'result'=>0,
                    'message'=>"Portefeuille non trouvé"
                ];
            }
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'result'=>null,
                'message'=>$th->getMessage()
            ];
        }
        return $response;
    }

    /**
     * credite_wallet function
     * Permet de créditer un wallet
     * @param Wallet $wallet
     * @param float $amount
     * @return array $result
     */
    public function credite_wallet(Wallet $wallet,float $amount, User $user = null){
        $response = [];
        try {
            if($wallet !== null){
                $solde = $wallet->balance;
                $new_solde = $solde+$amount;
                $res = $wallet->update([
                    'balance'=>$new_solde,
                ]);
                if($res){
                    $transact = $wallet->transactions()->create([
                        'user_id'=>$user != null ? $user->id : request()->user()->id,
                        'agency_id'=>$wallet->agency_id,
                        'amount'=>$amount,
                        'description'=>"Créditation du compte {$wallet->code}",
                        'status'=>1,
                        'type'=>1,
                        'wallet_id'=>$wallet->id,
                        'date_transact'=>date('Y-m-d H:i:s'),
                    ]);
                    $response = [
                        'success'=>true,
                        'result'=>[
                            'solde'=>$new_solde,
                            'transaction'=>$transact
                        ],
                        'message'=>"Compte crédité avec succès"
                    ];
                }else{
                    $response = [
                        'success'=>false,
                        'result'=>null,
                        'message'=>"Echec lors de la créditation de votre compte"
                    ];
                }
            }else{
                $response = [
                    'success'=>false,
                    'result'=>null,
                    'message'=>"Portefeuille introuvable"
                ];
            }
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'result'=>null,
                'message'=>$th->getMessage()
            ];
        }
        return $response;
    }

    /**
     * get_wallet function
     * Permet de récupérer un wallet
     * @param int $user_id
     * @return array $result
     */
    public function get_wallet(int $user_id){
        $response = [];
        try {
        $wallet = Wallet::where('user_id',$user_id)->with(['transactions'])->first();
            if($wallet !== null){
                $response = [
                    'success'=>true,
                    'result'=>$wallet,
                    'message'=>"Portefeuille trouvé"
                ];
            }else{
                $response = [
                    'success'=>false,
                    'result'=>null,
                    'message'=>"Portefeuille introuvable"
                ];
            }
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'result'=>null,
                'message'=>$th->getMessage()
            ];
        }
        return $response;
    }
}

