ROLES
	1) ROOT
	2) ADMIN
	3) CHEF AGENCE
	4) SUPERVISEUR
	5) CAISSIER
	6) COLLECTEUR
	7) LIVREUR
	8) CLIENT
	9) USER

#DEV
DB_DATABASE = u518811247_benilife_dev
DB_USERNAME = u518811247_benilife_dev
DB_PASSWORD = 6uA=KcUHxdf

#PROD
DB_DATABASE = u518811247_benilife_prod
DB_USERNAME = u518811247_benilife_prod
DB_PASSWORD = 6uA=KcUHxdf

ssh -p 65002 u518811247@*************

TO DO
1) add payment mode to versements
2) add transaction reference to versements


namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\ClientPerformance;

class RefreshClientPerformance extends Command
{
    protected $signature = 'client-performance:refresh';
    protected $description = 'Recalcule les statistiques des clients actifs et met à jour la table client_performances';

    public function handle()
    {
        $this->info("🔍 Recherche des clients impactés...");

        $isEmpty = ClientPerformance::count() === 0;

        if ($isEmpty) {
            $this->info("🆕 Aucune donnée trouvée. Mise à jour complète...");
            $idsToUpdate = [];
        } else {
            // Récupère les IDs des clients modifiés ces 6 dernières heures
            $recentClientIds = DB::select("
                SELECT DISTINCT c.id
                FROM clients c
                WHERE c.updated_at > NOW() - INTERVAL 6 HOUR

                UNION

                SELECT DISTINCT s.client_id
                FROM subscriptions s
                WHERE s.updated_at > NOW() - INTERVAL 6 HOUR

                UNION

                SELECT DISTINCT co.client_id
                FROM cotisations co
                WHERE co.updated_at > NOW() - INTERVAL 6 HOUR
            ");

            if (empty($recentClientIds)) {
                $this->info("✅ Aucun client à mettre à jour.");
                return;
            }

            $idsToUpdate = array_map(fn($row) => $row->id, $recentClientIds);
            $this->info("♻️ " . count($idsToUpdate) . " clients trouvés. Mise à jour en cours...");
        }

        // Suppression des anciennes données
        if (!empty($idsToUpdate)) {
            ClientPerformance::whereIn('id', $idsToUpdate)->delete();
        } else {
            ClientPerformance::truncate();
        }

        // Construction de la requête SQL
        $query = "
            SELECT
                c.id,
                c.nom,
                c.prenoms,
                c.phone,
                c.email,
                COUNT(DISTINCT CASE WHEN s.status IN ('finished', 'delivered') THEN s.id END) AS finished_subscriptions_count,
                COALESCE(SUM(s.carnet_price), 0) AS subscription_revenue,
                COUNT(DISTINCT CASE WHEN co.status = 'finished' THEN co.id END) AS finished_cotisations_count,
                COALESCE(SUM(co.total_amount), 0) AS cotisation_revenue,
                COALESCE(SUM(s.carnet_price), 0) + COALESCE(SUM(co.total_amount), 0) AS total_revenue,
                ROUND(
                    (COUNT(DISTINCT CASE WHEN s.status IN ('finished', 'delivered') THEN s.id END) * 0.4) +
                    (COALESCE(SUM(s.carnet_price), 0) / 10000 * 0.4) +
                    (COALESCE(SUM(co.total_amount), 0) / 10000 * 0.2)
                , 2) AS engagement_score
            FROM clients c
            LEFT JOIN subscriptions s ON c.id = s.client_id
            LEFT JOIN cotisations co ON c.id = co.client_id
        ";

        if (!empty($idsToUpdate)) {
            $query .= " WHERE c.id IN (" . implode(',', $idsToUpdate) . ")";
        }

        $query .= " GROUP BY c.id, c.nom, c.prenoms, c.phone, c.email";

        // Exécution de la requête
        $results = DB::select($query);

        if (empty($results)) {
            $this->info("✅ Aucun résultat trouvé.");
            return;
        }

        // Calcul du score max pour normalisation
        $scores = array_map(fn($r) => $r->engagement_score, $results);
        $maxScore = max($scores);

        foreach ($results as $row) {
            $normalizedScore = $maxScore > 0 ? round(($row->engagement_score / $maxScore) * 100, 2) : 0;
            $normalizedScore = min(100.00, $normalizedScore); // Limiter à 100

            ClientPerformance::create(array_merge((array)$row, [
                'last_updated' => now(),
                'engagement_score' => $normalizedScore
            ]));
        }

        if ($isEmpty) {
            $this->info("✅ Initialisation terminée : " . count($results) . " clients ajoutés !");
        } else {
            $this->info("✅ " . count($results) . " clients mis à jour !");
        }
    }
}
