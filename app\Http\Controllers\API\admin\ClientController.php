<?php

namespace App\Http\Controllers\API\admin;

use App\Http\Controllers\API\helpers\HelperController;
use App\Models\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ClientController extends HelperController
{
    public function getClients(Request $request)
    {
        $response = [];
        try {
            $page = $request->input('page', 1);
            $perPage = $request->input('limit', 15);
            $agency_id = $request->input('agency_id', null);

            $query = Client::orderBy('created_at', 'desc')
                ->with(['user', 'quarter', 'city', 'agency', 'collector'])
                ->withSum('cotisations', 'total_amount')
                ->withCount(['collectors', 'subscriptions', 'cotisations']);

            if ($agency_id !== null) {
                $query->where('agency_id', $agency_id);
            }

            $clients = $query->paginate($perPage, ['*'], 'page', $page);

            $response = [
                'success' => true,
                'message' => "Liste des clients",
                'result' => $clients,
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de récupération des clients",
                'result' => null,
                'errors' => $th->getMessage()
            ];
        }
        return response()->json($response);
    }


    public function add_client(Request $request) {}

    public function update_client(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'client_id' => 'required|exists:clients,id'
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec, vos données ne sont pas correctes",
                    'result' => null,
                ];
            } else {
                $client = Client::find($request->client_id);
                if ($client !== null) {
                    $client->nom = $request->nom;
                    $client->prenoms = $request->prenoms;
                    $client->phone = $request->phone;
                    $client->email = $request->email;
                    $client->adresse = $request->address;
                    $client->city_id = $request->city_id;
                    $client->quarter_id = $request->quarter_id;
                    $client->profession = $request->profession;
                    $client->save();

                    $response = [
                        'success' => true,
                        'message' => "Client mis à jour",
                        'result' => $client
                    ];
                } else {
                    $response = [
                        'success' => false,
                        'message' => "Client introuvable",
                        'result' => null
                    ];
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la mise à jour du client",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function get_client_details(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'client_id' => 'required|exists:clients,id'
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec, vos données ne sont pas correctes",
                    'result' => null,
                ];
            } else {
                $client = $this->get_client_byId($request->client_id);
                $details = $client->with([
                    'city',
                    'quarter',
                    'agency',
                    'collectors',
                    'packs',
                    'cotisations',
                    'packs' => function ($query) {
                        $query->with('products')->orderBy('created_at', 'desc');
                    },
                    'cotisations' => function ($query) {
                        $query->with(['subscription', 'collector', 'payments', 'agency']);
                    },
                    'subscriptions' => function ($query) {
                        $query->with(['cotisation' => function ($query) {
                            $query->with('payments')->orderBy('created_at', 'desc');
                        }])->orderBy('created_at', 'desc');
                    },
                    'collector' => function ($query) {},
                ])->whereId($client->id)->first();
                $response = [
                    'success' => true,
                    'message' => "Détails du client",
                    'result' => $details
                ];
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération du client",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }
}
