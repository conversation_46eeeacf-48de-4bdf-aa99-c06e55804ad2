<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class AddProductRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $validate = $this->validate_data($value);
        if (!$validate) {
            $fail($this->message());
        }
    }

    public function passes($attribute, $value)
    {
        //
    }

    public function message(): string
    {
        return 'Les données du produit ne sont pas correcte !';
    }

    public function validate_data(array $data){
        $is_valid = false;
        foreach ($data as $key => $value) {
            if ( isset($value['name']) && isset($value['price_achat']) && isset($value['price_vente']) &&
                isset($value['stock_quantity']) && isset($value['category_id'])) {
                $is_valid = true;
            } else {
                $is_valid = false;
                break;
            }
        }
        return $is_valid;
    }
}
