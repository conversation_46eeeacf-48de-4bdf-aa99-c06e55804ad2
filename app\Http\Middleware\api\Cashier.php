<?php

namespace App\Http\Middleware\api;

use App\Http\Controllers\API\helpers\HelperController;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class Cashier extends HelperController
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        $role_id = $user->role_id;
        $cashier_role = $this->get_role_byName("AGENT CAISSIER");
        if ($role_id == $cashier_role->id) {
            return $next($request);
        }else {
            return response()->json([
                'success'=>false,
                'message'=>'Vous n\'avez pas les droits pour effectuer cette action',
                'result'=>null
            ], 401);
        }
    }
}
