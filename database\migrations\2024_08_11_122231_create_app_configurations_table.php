<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('app_configurations', function (Blueprint $table) {
            $table->id();
            $table->enum('app_type', ['COLLECTOR', 'CASHIER', 'SUPERVISOR', 'DELIVERY']);
            $table->string("app_link")->nullable();
            $table->string("ios_app_link")->nullable();
            $table->string("app_version")->nullable();
            $table->string("ios_app_version")->nullable();
            $table->boolean("force_app_update")->default(false);
            $table->boolean("app_maintenance")->default(false);
            $table->json('file_config')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('app_configurations');
    }
};
