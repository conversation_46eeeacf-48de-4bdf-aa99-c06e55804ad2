<?php
use Illuminate\Support\Facades\Route;

Route::group(['namespace' => "agence", 'prefix' => 'agency', 'middleware' => ['auth:sanctum',]], function () {
    //CAISSE
    Route::group(['prefix' => 'caisse', 'middleware' => ['caisse']], function () {
        Route::post('/activities', 'CaisseController@get_caisse_activities');
        Route::post('/versements', 'CaisseController@get_caisse_versements');
        Route::middleware('secretCode')->group(function () {
            Route::post('/versements-late', '<PERSON><PERSON>seController@get_late_versements');
            Route::post('/versements/confirm', '<PERSON><PERSON>se<PERSON><PERSON>roll<PERSON>@confirm_versement');
        });
    });
});
