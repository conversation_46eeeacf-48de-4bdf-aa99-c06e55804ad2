<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Client extends Model
{
    use HasFactory;
    protected $fillable = [
        'nom','prenoms','address','quarter_id','phone','email','code','status',
        'localisation','profession','collector_id','user_id','agency_id','city_id',
    ];

    protected $casts = [
        'localisation' => 'array',
    ];

    public function city()
    {
        return $this->belongsTo(City::class, 'city_id',);
    }

    public function agency(){
        return $this->belongsTo(Agency::class,'agency_id','id');
    }

    public function quarter()
    {
        return $this->belongsTo(Quarter::class, 'quarter_id',);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id',);
    }

    /**
     * The collectors that belong to the Client
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function collectors(): BelongsToMany
    {
        return $this->belongsToMany(Personal::class, 'client_collectors', 'client_id', 'collector_id')
        ->withPivot(['agency_id','is_principal','status','collector_id','client_id'])
        ->withTimestamps();
    }

    /**
     * The principal_collector that belong to the Client
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function collector(): BelongsToMany
    {
        $res =  $this->belongsToMany(Personal::class, 'client_collectors', 'client_id', 'collector_id')
        ->withPivot(['agency_id','is_principal','status','collector_id','client_id'])
        ->withTimestamps()
        ->where('is_principal',true);
        return $res;
    }

    /**
     * Get all of the subscriptions for the Client
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class, 'client_id', 'id');
    }

    /**
     * The packs that belong to the Client
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function packs(): BelongsToMany
    {
        return $this->belongsToMany(Pack::class, 'subscriptions', 'client_id', 'pack_id')->withPivot(['started_at','finished_at','status','collector_id','agency_id','motif'])->withTimestamps();
    }

    /**
     * Get all of the cotisations for the Client
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function cotisations(): HasMany
    {
        return $this->hasMany(Cotisation::class, 'client_id', 'id');
    }

    /**
     * Get all of the payments for the Client
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class, 'client_id', 'id');
    }

    /**
     * Get all of the controls for the Client
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function controls(): HasMany
    {
        return $this->hasMany(Control::class, 'client_id', 'id');
    }


}
