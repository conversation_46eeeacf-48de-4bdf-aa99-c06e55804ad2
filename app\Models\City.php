<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class City extends Model
{
    use HasFactory;
    protected $fillable = [
        'name','address','country_id',
    ];

    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id',);
    }

    public function quarters()
    {
        return $this->hasMany(Quarter::class, 'city_id',);
    }

    public function agencies()
    {
        return $this->hasMany(Agency::class, 'city_id',);
    }

    public function clients()
    {
        return $this->hasMany(Client::class, 'city_id',);
    }

    /**
     * Get all of the personals for the City
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function personals(): HasMany
    {
        return $this->hasMany(Personal::class, 'city_id', 'id');
    }



}
