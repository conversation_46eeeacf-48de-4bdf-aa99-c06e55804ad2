<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('stocks')) {
            Schema::create('stocks', function (Blueprint $table) {
                $table->id();
                $table->string('reference');
                $table->string('name')->nullable();
                $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
                $table->enum('movement', ['entry', 'output'])->default('entry');
                $table->integer('quantity')->default(0);
                $table->string('status')->default('active');
                $table->date('date_stock')->nullable();
                $table->text('description')->nullable();
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stocks');
    }
};
