<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AgencyPersonal extends Model
{
    use HasFactory;
    protected $fillable = [
        'agency_id','personal_id','status','affected_at','mutated_at','is_current',
        'role_id'
    ];

    public function agency()
    {
        return $this->belongsTo(Agency::class, 'agency_id');
    }

    public function personal()
    {
        return $this->belongsTo(Personal::class, 'personal_id');
    }

    public function versements()
    {
        return $this->hasMany(Versement::class, 'collector_id', 'personal_id');
    }

    /**
     * Get all of the cashier_versements for the AgencyPersonal
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function cashier_versements(): HasMany
    {
        return $this->hasMany(Versement::class, 'cashier', 'personal_id');
    }

    public function cotisations()
    {
        return $this->hasMany(Cotisation::class, 'collector_id', 'personal_id');
    }

    public function payments()
    {
        return $this->hasMany(Payment::class, 'collector_id', 'personal_id');
    }

    public function role()
    {
        return $this->belongsTo(Role::class, 'role_id');
    }

    /**
     * Get all of the clients for the AgencyPersonal
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function clients(): HasMany
    {
        return $this->hasMany(Client::class, 'collector_id', 'personal_id');
    }

    /**
     * Get all of the subscriptions for the AgencyPersonal
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class, 'collector_id', 'personal_id');
    }
}
