<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('agencies', function (Blueprint $table) {
            $table->id();
            $table->boolean('is_parent')->default(false);
            $table->integer('parent_id')->nullable();
            $table->string('name');
            $table->integer('city_id');
            $table->integer('quarter_id')->nullable();
            $table->string('address')->nullable();
            $table->string('phone')->nullable();
            $table->string('email')->nullable();
            $table->string('code')->nullable();
            $table->integer('responsable_id')->nullable();
            $table->enum('status',['active','inactive'])->default('active');
            $table->json('localisation')->nullable();
            $table->text('perimeters')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('agencies');
    }
};
