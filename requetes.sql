-- FIX PAYMENTS DATA
-- =========ETAPE 1=========
Select * from payments
where cotisation_id IS NULL
and subscription_id IS NOT NULL;

update payments set amount = 200
where cotisation_id IS NULL
and subscription_id IS NOT NULL;

-- ===Mettre à jour le type de paiement pour les cotisations et les ventes de carnets===

update payments set payment_type = 'CARNET'
where cotisation_id IS NULL
and subscription_id IS NOT NULL;

update payments set payment_type = 'COLLECT'
where cotisation_id IS NOT NULL
and subscription_id IS NOT NULL;


-- =========ETAPE 2 (Calcule du chiffre d'affaires par COLLECTEUR)=========
SELECT
    collector_id,c.user_id, c.nom, c.prenoms,
    SUM(amount) AS TOTAL_CA
FROM
    payments p
INNER JOIN personals c ON c.id = p.collector_id

GROUP BY
    collector_id
ORDER BY TOTAL_CA DESC;



SELECT
    p.collector_id,
    c.user_id,
    c.nom,
    c.prenoms,
    SUM(CASE
        WHEN p.subscription_id IS NOT NULL AND p.cotisation_id IS NULL THEN p.amount
        ELSE 0
    END) AS total_revenue_subscriptions,
    SUM(CASE
        WHEN p.subscription_id IS NOT NULL AND p.cotisation_id IS NOT NULL THEN p.amount
        ELSE 0
    END) AS total_revenue_cotisations,
    SUM(CASE
        WHEN p.subscription_id IS NOT NULL AND p.cotisation_id IS NULL THEN p.amount
        ELSE 0
    END) +
    SUM(CASE
        WHEN p.subscription_id IS NOT NULL AND p.cotisation_id IS NOT NULL THEN p.amount
        ELSE 0
    END) AS total_ca
FROM
    payments p
INNER JOIN personals c ON c.id = p.collector_id
GROUP BY
    p.collector_id,
    c.user_id,
    c.nom,
    c.prenoms
ORDER BY
    total_revenue_subscriptions DESC,
    total_revenue_cotisations DESC;

UPDATE wallets
SET balance = CASE
    WHEN user_id IN (4, 10, 12, 8, 11, ) THEN CASE user_id
        WHEN 4 THEN 1430875.00
        WHEN 10 THEN 690350.00
        WHEN 12 THEN 377775.00
        WHEN 8 THEN 192900.00
        WHEN 11 THEN 63834.00
        ELSE balance -- Garde le solde actuel si l'utilisateur ne correspond à aucun cas interne
    END
    ELSE 0.00 -- Met la balance à zéro pour tous les autres utilisateurs
END;


-- ===METTRE A JOUR LE SOLDE(CHIFFRE D'AFFAIRES) DES WALLETS POUR LES COLLECTEURS===

UPDATE wallets w
INNER JOIN (
    SELECT
        c.id AS collector_id,
        u.id AS user_id,
        SUM(p.amount) AS TOTAL_CA
    FROM payments p
    INNER JOIN personals c ON p.collector_id = c.id
    INNER JOIN users u ON c.user_id = u.id
    GROUP BY c.id, u.id
) AS totals ON w.user_id = totals.user_id
SET w.solde = totals.TOTAL_CA;

update payments set payment_type = 'CARNET'
where subscription_id = 563
and cotisation_id IS NULL;

update payments set payment_type = 'COLLECT'
where subscription_id = 563
and cotisation_id = 389;

SELECT *
FROM `payments`
where payment_type IS NULL;

UPDATE payments set payment_type = 'COLLECT'
where subscription_id IS NOT NULL
AND cotisation_id IS NOT NULL;

UPDATE payments set payment_type = 'CARNET'
where subscription_id IS NOT NULL
AND cotisation_id IS NULL;

SELECT
    subscription_id,
    `key`,
    month,
    COUNT(*) AS duplicate_count
FROM
    payments
WHERE
    payment_type = 'COLLECT'
GROUP BY
    subscription_id,
    `key`,
    month
HAVING
    COUNT(*) > 1;


mysql -u u518811247_iziway_dev  -p j[9XLF4@v]A u518811247_iziway_dev < /home/<USER>/domains/benilife.xyz/public_html/izicollect_part_5.sql;

source /home/<USER>/domains/benilife.xyz/public_html/izicollect_part_5.sql;

mysql db_name < backup-file.sql;

mysql u518811247_iziway_dev < /home/<USER>/domains/benilife.xyz/public_html/izicollect_part_5.sql;

CREATE TABLE payments (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    amount DOUBLE(12,2) NOT NULL,
    payment_date DATETIME NOT NULL,
    month INT NULL,
    metadata JSON NULL,
    `key` INT NULL,
    status ENUM('pending', 'completed', 'paid', 'canceled') DEFAULT 'pending',
    agency_id INT NOT NULL,
    collector_id INT NOT NULL,
    subscription_id INT NULL,
    cotisation_id INT NULL,
    pack_id INT NULL,
    payment_mode ENUM('CASH', 'CHEQUE', 'VIREMENT', 'MOBILEMONEY') DEFAULT 'CASH',
    payment_type ENUM('CARNET', 'COLLECT', 'OTHER') NULL,
    description TEXT NULL,
    created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    client_id VARCHAR(255) NULL,
    versement_id INT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



SELECT
    c.id,
    c.nom,
    c.prenoms,
    c.phone,
    -- Nombre de carnets terminés
    COUNT(DISTINCT CASE WHEN s.status IN ('finished', 'delivered') THEN s.id END) AS finished_subscriptions_count,
    -- CA total des souscriptions
    COALESCE(SUM(s.carnet_price), 0) AS subscription_revenue,
    -- CA total des cotisations
    COALESCE(SUM(co.total_amount), 0) AS cotisation_revenue,
    -- CA global
    COALESCE(SUM(s.carnet_price), 0) + COALESCE(SUM(co.total_amount), 0) AS total_revenue
FROM
    clients c
LEFT JOIN
    subscriptions s ON c.id = s.client_id
LEFT JOIN
    cotisations co ON c.id = co.client_id
GROUP BY
    c.id, c.nom, c.prenoms, c.phone, c.email
ORDER BY
    total_revenue DESC
LIMIT 10;

-- Liste des packs populaires

SELECT
    p.id,
    p.name,
    p.description,
    p.carnet_price,
    -- Nombre total de souscriptions
    COUNT(DISTINCT s.id) AS total_subscriptions,
    -- Nombre de souscriptions actives (finished/delivered)
    COUNT(DISTINCT CASE WHEN s.status IN ('finished', 'delivered') THEN s.id END) AS active_subscriptions,
    -- Revenu total généré par les souscriptions
    COALESCE(SUM(s.carnet_price), 0) AS subscription_revenue,
    -- Nombre de cotisations liées à ce pack
    COUNT(DISTINCT co.id) AS total_cotisations,
    -- Cotisations terminées
    COUNT(DISTINCT CASE WHEN co.status = 'finished' THEN co.id END) AS completed_cotisations,
    -- Revenu total des cotisations
    COALESCE(SUM(co.total_amount), 0) AS cotisation_revenue,
    -- Popularity score (pondération personnalisable)
    (COUNT(DISTINCT s.id) * 0.6) + (COUNT(DISTINCT co.id) * 0.4) AS popularity_score
FROM
    packs p
LEFT JOIN
    subscriptions s ON p.id = s.pack_id
LEFT JOIN
    cotisations co ON s.id = co.subscription_id  -- Supposant que cotisations est lié à subscriptions
GROUP BY
    p.id, p.name, p.description, p.carnet_price
ORDER BY
    popularity_score DESC,
    active_subscriptions DESC,
    subscription_revenue DESC;


-- Liste des 20 meilleurs packs populaires.

SELECT
    p.id,
    p.name AS pack_name,
    p.carnet_price,
    COUNT(DISTINCT s.id) AS total_subscriptions,
    SUM(s.status IN ('finished', 'delivered')) AS successful_subscriptions,
    IFNULL(SUM(s.carnet_price), 0) AS subscription_revenue,
    COUNT(DISTINCT co.id) AS total_cotisations,
    SUM(co.status = 'finished') AS completed_cotisations,
    IFNULL(SUM(co.total_amount), 0) AS cotisation_revenue,
    ROUND(
        (COUNT(DISTINCT s.id) * 0.5) +
        (SUM(s.status IN ('finished', 'delivered')) * 0.3) +
        (IFNULL(SUM(s.carnet_price), 0) / 10000 * 0.2)
    , 2) AS performance_score
FROM
    packs p
LEFT JOIN
    subscriptions s ON p.id = s.pack_id
LEFT JOIN
    cotisations co ON s.id = co.subscription_id
GROUP BY
    p.id, p.name, p.carnet_price
ORDER BY
    performance_score DESC,
    successful_subscriptions DESC
LIMIT 20;
