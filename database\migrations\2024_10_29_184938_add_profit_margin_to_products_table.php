<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->enum('profit_margin_type', ['percent', 'amount'])->default('amount')->nullable()->after('price_vente');
            $table->decimal('margin_profit', 10, 2)->default(0)->nullable()->after('profit_margin_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn('profit_margin_type');
            $table->dropColumn('margin_profit');
        });
    }
};
