# Projet Benilife

## P<PERSON>requis

Assurez-vous d'avoir les outils suivants installés sur votre machine :

- [<PERSON><PERSON>](https://docs.docker.com/get-docker/)
- [Docker Compose](https://docs.docker.com/compose/install/)

## Installation

1. C<PERSON>z le dépôt :

    ```sh
    git clone https://gitlab.com/frikalab-group/benilife/api-benilife.git
    cd votre-repository
    ```

2. Créez un fichier `.env` à la racine du projet en copiant le modèle fourni :

    ```sh
    cp .env.example .env
    ```

3. Modifiez les variables d'environnement dans le fichier `.env` selon vos besoins.

4. Lancez les services Docker :

    ```sh
    docker-compose -p benilife up -d --build
    ```

## Utilisation

- Accédez à votre application sur [http://localhost:8000](http://localhost:8000).
- Adminer sera disponible sur [http://localhost:8080](http://localhost:8080).

## Arrêter les services

Pour arrêter les services Docker, utilisez la commande suivante :

```sh
docker-compose -p benilife down
