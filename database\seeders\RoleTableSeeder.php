<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class RoleTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            [
                'name' => 'ROOT',
                'description' => 'Super Administrateur',
            ],
            [
                'name' => 'ADMIN',
                'description' => 'Administrateur',
            ],
            [
                'name' => 'CHEF AGENCE',
                'description' => 'Chef d\'agence',
            ],
            [
                'name' => 'AGENT SUPERVISEUR',
                'description' => 'Agent superviseur',
            ],
            [
                'name' => 'AGENT CAISSIER',
                'description' => 'Agent caissier',
            ],
            [
                'name' => 'AGENT COLLECTEUR',
                'description' => 'Agent collecteur',
            ],
            [
                'name' => 'AGENT LIVREUR',
                'description' => 'Agent livreur',
            ],
            [
                'name' => 'CLIENT',
                'description' => 'client',
            ],
            [
                'name' => 'USER',
                'description' => 'Utilisateur',
            ],
            [
                'name' => 'SECRETAIRE',
                'description' => 'Sec<PERSON>taire',
            ],
            [
                'name' => 'MAGASINIER',
                'description' => 'Magasinier',
            ],
            [
                'name' => 'COMPTABLE',
                'description' => 'Comptable',
            ],
            [
                'name' => 'RH',
                'description' => 'Ressources Humaines',
            ]
        ];

        foreach ($roles as $role) {
            \App\Models\Role::create($role);
        }
    }
}
