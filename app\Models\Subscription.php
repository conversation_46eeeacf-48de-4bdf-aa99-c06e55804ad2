<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Subscription extends Model
{
    use HasFactory;
    protected $fillable = [
        'pack_id','client_id','started_at','finished_at','status','collector_id','agency_id',
        'motif','code','price','carnet_price','versement_id'
    ];

    protected $casts = [
        'price' => 'int'
    ];


    public function pack()
    {
        return $this->belongsTo(Pack::class, 'pack_id');
    }

    public function client()
    {
        return $this->belongsTo(Client::class, 'client_id');
    }

    public function collector(){
        return $this->belongsTo(Personal::class, 'collector_id');
    }

    public function agency(){
        return $this->belongsTo(Agency::class, 'agency_id');
    }

    public function payments()
    {
        return $this->hasMany(Payment::class, 'subscription_id');
    }

    public function cotisation()
    {
        return $this->hasOne(Cotisation::class, 'subscription_id', 'id');
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    public function scopeCanceled($query)
    {
        return $query->where('status', 'canceled');
    }

    public function scopeFinished($query)
    {
        return $query->where('status', 'finished');
    }


}
