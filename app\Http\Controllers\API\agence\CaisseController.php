<?php

namespace App\Http\Controllers\API\agence;

use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Services\FirebaseService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\API\Traits\WalletTrait;
use App\Http\Controllers\API\agence\AgencyController;

class CaisseController extends AgencyController
{
    use WalletTrait;

    protected $firebaseService;

    public function __construct(FirebaseService $firebaseService) {
        $this->firebaseService = $firebaseService;
    }

    public function get_caisse_versements(Request $request)
    {
        $response = [];
        $status = 200;
        $filter = $request->filter;

        try {
            // Récupération du caissier et de son portefeuille
            $cashier = $this->get_personal_auth($request);
            $wallet = $cashier->user->wallet;

            // Récupération de l'agence actuelle et de ses détails
            $agency = $cashier->currentAgency()->first();
            if (!$agency) {
                throw new \Exception("L'agence actuelle n'a pas pu être trouvée.");
            }
            $city = $agency->city;
            $quarter = $agency->quarter;

            // Requête des versements avec filtre conditionnel
            $versementsQuery = $cashier->cashier_versements()->with(['agency', 'collector', 'cashier'])->orderBy('created_at', 'desc');

            if ($filter && in_array($filter, ['initialized', 'in_progress', 'completed', 'confirmed'])) {
                $versementsQuery->where('status', $filter);
            }

            // Récupération des versements
            $versements = $versementsQuery->get();

            // Construction de la réponse
            $response = [
                'success' => true,
                'message' => "Liste des versements de l'agence",
                'result' => [
                    'cashier' => $cashier,
                    'agency' => $agency,
                    'city' => $city,
                    'quarter' => $quarter,
                    'wallet' => $wallet,
                    'versements' => $versements,
                    'filter' => $filter,
                    'request' => $request->all()
                ]
            ];
        } catch (\Throwable $th) {
            // Gestion des erreurs
            $response = [
                'success' => false,
                'message' => "Échec de la récupération des versements de l'agence",
                'result' => null,
                'errors' => $th->getMessage() // Peut être remplacé par un message générique si nécessaire
            ];
            $status = 500;
        }

        return $this->apiResponse($response, $status);
    }


    /**
     * get_late_versements function
     *
     * @param Request $request
     * @return void
     */
    public function get_late_versements(Request $request){
        $response = [];
        $status = 200;
        try {
            $cashier = $this->get_personal_auth($request);
            $agency = $cashier->currentAgency()->first();

            $versements = $cashier->versements()->where('agency_id',$agency->id)->where('amount_remaining','>',0)->with(['agency','collector'])->get();
            $response = [
                'success'=>true,
                'message'=>"Liste des versements en retards",
                'result'=>$versements
            ];
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la récupération des versements en retards",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * confirm_versement_old function
     *  Confirmer un versement
     * @param Request $request
     * @return void
     */
    public function confirm_versement_old(Request $request){
        $response = [];
        $status = 200;

        try {
            $validate = Validator::make($request->all(),[
                'qrcode'=>'required',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success'=>false,
                    'message'=>"Echec de validation des données",
                    'result'=>null,
                    'errors'=>$validate->getMessageBag()
                ];
            } else {
                $cashier = $this->get_personal_auth($request);
                $agency = $cashier->currentAgency()->first();
                $versement = $this->get_versement_by_Qrcode($request->qrcode);
                if ($versement == null) {
                    $response = [
                        'success'=>false,
                        'message'=>"Le versement n'existe pas",
                        'result'=>null,
                        'errors'=>null
                    ];
                } else {
                    $now = date('Y-m-d H:i:s');
                    if ($versement->expired_at < $now) {
                        $response = [
                            'success'=>false,
                            'message'=>"Le versement a expiré",
                            'result'=>null,
                            'errors'=>null
                        ];
                    }else{
                        // S'assurer que le versement peut etre traiter
                        if (!in_array($versement->status, ['completed', 'in_progress'])) {
                            $response = [
                                'success' => false,
                                'message' => "Cet versement a été dejà traité!",
                                'result' => null,
                                'errors' => null
                            ];
                        }else {
                            DB::beginTransaction();
                            $collector = $versement->collector;
                            $co_agency = $collector->currentAgency()->first();
                            // $agency = $versement->agency;
                            // dd($collector->currentAgency()->first());
                            if ($co_agency->id === $agency->id) {
                                $agency_wallet = $agency->wallet()->first();
                                $cashier_wallet = $cashier->user->wallet;
                                // dd($agency_wallet);
                                // dd($cashier_wallet);
                                $collector_wallet = $collector->user->wallet;
                                $debit = $this->wallet_debite($collector_wallet,$versement->amount, $cashier->user);
                                $up_collector_wallet_solde = $collector_wallet->update(
                                    [
                                        'solde' => $collector_wallet->solde + $versement->amount
                                    ]
                                );
                                Log::info('up_collector_wallet_solde: '.json_encode($up_collector_wallet_solde));
                                if ($debit['success'] && $up_collector_wallet_solde !== null) {
                                    $credit = $this->credite_wallet($agency_wallet,$versement->amount, $cashier->user);
                                    $credit2 = $this->credite_wallet($cashier_wallet,$versement->amount, $cashier->user);
                                    if ($credit['success'] && $credit2['success']) {
                                        $unpaidActivities = $this->getCollectorUnpaidActivities($collector);
                                        $unpaidSubscriptions = $unpaidActivities['subscriptions'];
                                        $unpaidPayments = $unpaidActivities['collects'];
                                        $total_amount = $unpaidActivities['total_amount'];
                                        $versement->update([
                                            'status'=>'confirmed',
                                            'confirmed_at'=>date('Y-m-d H:i:s'),
                                            // 'amount_remaining'=>0,
                                            'expired_at'=>null,
                                            'cashier_id'=>$cashier->id
                                        ]);
                                        // Mettre à jour les ventes de carnets et les collectes de cotisations avec l'ID du versement

                                        foreach ($unpaidSubscriptions as $subscription) {
                                            $subscription->update([
                                                'versement_id'=>$versement->id
                                            ]);
                                        }
                                        foreach ($unpaidPayments as $payment) {
                                            $metadata = $payment->metadata;
                                            $count_status = 0;
                                            $metadata = array_map(function ($item, int $index) use ($versement, $count_status) {
                                                $metadata_status = (array_key_exists('status', $item) && $item['status'] == 'completed') ? 'paid' : $item['status'];
                                                if ($metadata_status == 'paid') {
                                                    $count_status++;
                                                }
                                                return [
                                                    'id' => $item['id'],
                                                    'key' => $item['key'],
                                                    'month' => $item['month'],
                                                    'amount' => $item['amount'],
                                                    'payment_date' => $item['payment_date'],
                                                    'status' => $metadata_status,
                                                    'versement_id' => (array_key_exists('versement_id', $item) && $item['versement_id'] != null) ? $item['versement_id'] : $versement->id,
                                                ];
                                            }, $metadata, array_keys($metadata));
                                            $payment_status = $count_status == 31 ? 'paid' : $payment->status;
                                            $payment->update([
                                                'metadata' => $metadata,
                                                'status' => $payment_status
                                            ]);

                                        }

                                        //Mettre à jour le solde du wallet du collecteur
                                        $new_solde = $collector_wallet->solde + $versement->amount;
                                        $collector_wallet->update([
                                            'solde'=>$new_solde
                                        ]);

                                        DB::commit();
                                        $response = [
                                            'success'=>true,
                                            'message'=>"Versement confirmé et traité avec succès",
                                            'result'=>[
                                                'versement'=>$versement,
                                                'debit'=>$debit['result'],
                                                'credit'=>$credit['result'],
                                                'credit2'=>$credit2['result'],
                                                'cashier'=>$cashier,
                                                'collector'=>$collector,
                                            ]
                                        ];

                                        // send push notification
                                        // Cahier
                                        $title = "Versement confirmé";
                                        $body = "Vous venez de confirmer un versement de {$versement->amount} FCFA du collector {$collector->nom} {$collector->prenoms}";
                                        if($cashier->user->device_token !== null){
                                            $this->firebaseService->sendNotification($cashier->user->device_token, $title, $body);
                                        }
                                        // Collector
                                        $title = "Versement";
                                        $body = "Votre versement de {$versement->amount} FCFA vient d'être confirmé par le caisse {$cashier->user->username}";
                                        if($collector->user->device_token !== null){
                                            $this->firebaseService->sendNotification($collector->user->device_token, $title, $body);
                                        }
                                        // send push notification to other active and connected admins
                                        $active_connected_admins_users_device_tokens = $this->get_active_connected_users_device_tokens_by_roles([1,2,3]);
                                        if($active_connected_admins_users_device_tokens['success'] == true){
                                            $tokens = $active_connected_admins_users_device_tokens['result'];
                                            if (count($tokens) > 0) {
                                                $body = "Un versement de {$versement->amount} FCFA vient d'être confirmé par la caisse {$cashier->user->username}";
                                                $this->firebaseService->sendNotifications($tokens, $title, $body);
                                            }
                                        }
                                    }else{
                                        $response = [
                                            'success'=>false,
                                            'message'=>"Echec de la confirmation du versement",
                                            'result'=>null,
                                            'errors'=>$credit['message']
                                        ];
                                    }
                                }else{
                                    $response = [
                                        'success'=>false,
                                        'message'=>"Echec de la confirmation du versement",
                                        'result'=>null,
                                        'errors'=>$debit['message']
                                    ];
                                }
                            }else {
                                $response = [
                                    'success'=>false,
                                    'message'=>"Cet versement n'est pas pour cette agence",
                                    'result'=>null,
                                    'errors'=>null
                                ];
                            }
                        }
                    }
                }
            }
        } catch (\Throwable $th) {
            DB::rollback();
            $response = [
                'success'=>false,
                'message'=>"Echec de la confirmation du versement",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * confirm_versement function
     *  Confirmer un versement
     * @param Request $request
     * @return void
    */
    public function confirm_versement(Request $request)
    {
        $response = [];
        $status = 200;

        try {
            // Validation des données d'entrée
            $validator = Validator::make($request->all(), [
                'qrcode' => 'required',
            ]);

            if ($validator->fails()) {
                return $this->apiResponse([
                    'success' => false,
                    'message' => "Échec de validation des données",
                    'errors' => $validator->getMessageBag()
                ], 400);
            }

            // Récupération du caissier et de l'agence
            $cashier = $this->get_personal_auth($request);
            $agency = $cashier->currentAgency()->first();
            $versement = $this->get_versement_by_Qrcode($request->qrcode);

            if (!$versement) {
                return $this->apiResponse([
                    'success' => false,
                    'message' => "Le versement n'existe pas",
                ], 404);
            }

            $now = Carbon::now();
            if ($versement->expired_at < $now) {
                return $this->apiResponse([
                    'success' => false,
                    'message' => "Le versement a expiré",
                ], 400);
            }

            // Vérification du statut du versement
            if (!in_array($versement->status, ['completed', 'in_progress'])) {
                return $this->apiResponse([
                    'success' => false,
                    'message' => "Ce versement a déjà été traité!",
                ], 400);
            }

            // Début de la transaction
            DB::beginTransaction();

            // Récupération du collecteur et de l'agence du collecteur
            $collector = $versement->collector;
            $co_agency = $collector->currentAgency()->first();

            // if ($co_agency->id !== $agency->id) {
            //     DB::rollBack();
            //     return $this->apiResponse([
            //         'success' => false,
            //         'message' => "Ce versement n'est pas destiné à cette agence",
            //     ], 403);
            // }

            // Traitement des portefeuilles
            $collector_wallet = $collector->user->wallet;
            $agency_wallet = $agency->wallet()->first();
            $cashier_wallet = $cashier->user->wallet;

            $debit = $this->wallet_debite($collector_wallet, $versement->amount, $cashier->user);
            if (!$debit['success']) {
                DB::rollBack();
                return $this->apiResponse([
                    'success' => false,
                    'message' => "Échec de la confirmation du versement",
                    'errors' => $debit['message']
                ], 400);
            }

            $collector_wallet->increment('solde', $versement->amount);

            $credit_agency = $this->credite_wallet($agency_wallet, $versement->amount, $cashier->user);
            $credit_cashier = $this->credite_wallet($cashier_wallet, $versement->amount, $cashier->user);

            if (!$credit_agency['success'] || !$credit_cashier['success']) {
                DB::rollBack();
                return $this->apiResponse([
                    'success' => false,
                    'message' => "Échec de la confirmation du versement",
                    'errors' => $credit_agency['message'] ?? $credit_cashier['message']
                ], 400);
            }

            // Mise à jour du versement
            $versement->update([
                'status' => 'confirmed',
                'confirmed_at' => $now,
                'expired_at' => null,
                'cashier_id' => $cashier->id
            ]);

            // Mise à jour des ventes de carnets et collectes avec l'ID du versement
            $unpaidActivities = $this->getCollectorUnpaidActivities($collector);
            $this->updateUnpaidSubscriptions($unpaidActivities['subscriptions'], $versement->id);
            $this->updateUnpaidPayments($unpaidActivities['collects'], $versement->id);

            DB::commit();

            // Envoi des notifications push
            $this->sendVersementNotification($cashier, $collector, $versement->amount);

            return $this->apiResponse([
                'success' => true,
                'message' => "Versement confirmé et traité avec succès",
                'result' => [
                    'versement' => $versement,
                    'debit' => $debit['result'],
                    'credit_agency' => $credit_agency['result'],
                    'credit_cashier' => $credit_cashier['result'],
                    'cashier' => $cashier,
                    'collector' => $collector,
                ]
            ]);

        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->apiResponse([
                'success' => false,
                'message' => "Échec de la confirmation du versement",
                'errors' => $th->getMessage()
            ], 500);
        }
    }

    /**
     * Mettre à jour les abonnements impayés.
     */
    private function updateUnpaidSubscriptions($unpaidSubscriptions, $versementId)
    {
        foreach ($unpaidSubscriptions as $subscription) {
            $subscription->update([
                'versement_id' => $versementId,
                'status' => 'paid'
            ]);
        }
    }

    /**
     * Mettre à jour les paiements impayés.
     */
    private function updateUnpaidPayments($unpaidPayments, $versementId)
    {
        Log::info('Updating unpaid payments: ' . json_encode($unpaidPayments));

        foreach ($unpaidPayments as $payment) {
            // Vérifier si $payment->metadata est une chaîne JSON ou déjà un tableau
            if (is_string($payment->metadata)) {
                $metadata = json_decode($payment->metadata, true);

                if (json_last_error() !== JSON_ERROR_NONE) {
                    Log::error('Invalid JSON in payment metadata: ' . $payment->metadata);
                    continue; // Passer au paiement suivant si le JSON est invalide
                }
            } elseif (is_array($payment->metadata)) {
                $metadata = $payment->metadata; // Utiliser directement le tableau
            } else {
                Log::error('Invalid metadata type in payment: ' . gettype($payment->metadata));
                continue; // Passer au paiement suivant si le type est invalide
            }

            Log::info('Updating unpaid payments metadata: ' . json_encode($metadata));

            // Mettre à jour le statut et ajouter versement_id si nécessaire
            $metadata = array_map(function ($item) use ($versementId) {
                $item['status'] = 'paid';
                $item['versement_id'] = $item['versement_id'] ?? $versementId;
                return $item;
            }, $metadata);

            // Vérifier si tous les statuts sont 'paid'
            $allPaid = array_reduce($metadata, function ($carry, $item) {
                return $carry && ($item['status'] === 'paid');
            }, true);

            // Mettre à jour le statut du paiement
            $payment->update([
                'metadata' => $metadata,
                'status' => $allPaid ? 'paid' : $payment->status,
            ]);
        }
    }

    /**
     * Envoyer les notifications push après le versement.
     */
    private function sendVersementNotification($cashier, $collector, $amount)
    {
        // Notification pour le caissier
        if ($cashier->user->device_token) {
            $title = "Versement confirmé";
            $body = "Vous avez confirmé un versement de {$amount} FCFA du collecteur {$collector->nom} {$collector->prenoms}";
            $this->firebaseService->sendNotification($cashier->user->device_token, $title, $body);
        }

        // Notification pour le collecteur
        if ($collector->user->device_token) {
            $title = "Versement";
            $body = "Votre versement de {$amount} FCFA a été confirmé par le caissier {$cashier->user->username}";
            $this->firebaseService->sendNotification($collector->user->device_token, $title, $body);
        }

        // Notifications pour les administrateurs actifs et connectés
        $activeAdminsTokens = $this->get_active_connected_users_device_tokens_by_roles([1, 2, 3]);
        if ($activeAdminsTokens['success'] && count($activeAdminsTokens['result']) > 0) {
            $title = "Versement confirmé";
            $body = "Un versement de {$amount} FCFA a été confirmé par la caisse {$cashier->user->username}";
            $this->firebaseService->sendNotifications($activeAdminsTokens['result'], $title, $body);
        }
    }



    /**
     * get_caisse_activities function
     *  Récupérer les activités de la caisse
     * @param Request $request
     * @return void
     */
    public function get_caisse_activities(Request $request){
        $response = [];
        $status = 200;
        try {
            $filter = $request->filter;
            $type = $filter['type'];
            $value = $filter['value'];
            $cashier = $this->get_personal_auth($request);
            $agency = $cashier->currentAgency()->first();
            switch ($type) {
                case 'day':
                    if (isset($value)) {
                        $versements = $cashier->versements()->where('agency_id',$agency->id)->where('status','confirmed')->whereDate('created_at',$value)->with(['agency','collector'])->orderBy('created_at', 'desc')->get();
                    } else {
                        $versements = $cashier->versements()->where('agency_id',$agency->id)->where('status','confirmed')->whereDate('created_at',date('Y-m-d'))->with(['agency','collector'])->orderBy('created_at', 'desc')->get();
                    }
                    break;

                case 'week':
                    $versements = $cashier->versements()->where('agency_id',$agency->id)->whereBetween('created_at',[
                        Carbon::now()->startOfWeek(),
                        Carbon::now()->endOfWeek()
                    ])->orderBy('created_at', 'desc')->get();
                    break;

                case 'month':
                    if (isset($value)) {
                        $versements = $cashier->versements()->where('agency_id',$agency->id)->where('status','confirmed')->whereMonth('created_at',$value)->with(['agency','collector'])->orderBy('created_at', 'desc')->get();
                    } else {
                        $versements = $cashier->versements()->where('agency_id',$agency->id)->where('status','confirmed')->whereMonth('created_at',date('m'))->with(['agency','collector'])->orderBy('created_at', 'desc')->get();
                    }
                    break;

                case 'year':
                    if (isset($value)) {
                        $versements = $cashier->versements()->where('agency_id',$agency->id)->where('status','confirmed')->whereYear('created_at',$value)->with(['agency','collector'])->orderBy('created_at', 'desc')->get();
                    } else {
                        $versements = $cashier->versements()->where('agency_id',$agency->id)->where('status','confirmed')->whereYear('created_at',date('Y'))->with(['agency','collector'])->orderBy('created_at', 'desc')->get();
                    }
                    break;

                default:
                    $versements = $cashier->versements()->where('agency_id',$agency->id)->whereBetween('created_at',[
                        Carbon::now()->startOfMonth(),
                        Carbon::now()->endOfMonth()
                    ])->orderBy('created_at', 'desc')->get();
                break;
            }
            $response = [
                'success'=>true,
                'message'=>"Liste des versements",
                'result'=>$versements
            ];

        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la récupération des versements",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    public function init_late_versement(Request $request){

    }
}
