<?php
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes                                                 |
|--------------------------------------------------------------------------
|                                                    |
| Here is where you can register API routes for your app. |
|                                                    |                                                    |                                                    |
*/

Route::group(['namespace' => "agence", 'prefix' => 'agency', 'middleware' => ['auth:sanctum']], function () {
    Route::prefix('collector')->group(function () {
        Route::post('/infos', 'CollectorController@get_collector_details');
        Route::get('/profile', 'CollectorController@get_collector_profile');
        Route::get('/clients', 'CollectorController@get_clients');
        Route::post('/clients/details', 'Collector<PERSON>ontroller@get_client_details');
        Route::get('/clients/search', 'CollectorController@search_client');
        Route::post('/clients/cotisations', 'CollectorController@get_client_cotisations');
        Route::post('/versements', 'CollectorController@get_all_versements');
        Route::get('/current-activities', 'CollectorController@getCurrentCollectorActivities');
        Route::get('/current-activities-v2', 'CollectorController@getCurrentCollectorActivitiesV2');
        Route::get('/packs', 'CollectorController@get_all_packs');


        Route::middleware('secretCode')->group(function () {
            Route::post('/clients/add', 'CollectorController@add_client');
            // Route::post('/clients/add-cotisation', 'CollectorController@add_client_cotisation');
            Route::post('/clients/add-cotisation/v2', 'CollectorController@add_cotisation_withKeys');
            Route::post('/clients/add-cotisation/v3', 'CollectorController@addcCtisationWithKeysV2');
            Route::post('/clients/subscribe', 'CollectorController@add_client_subscription');
            Route::post('/versements/add', 'CollectorController@init_versement');
            Route::post('/versements/reset-expiration', 'CollectorController@reset_versement_expiration');
        });
    });
});
