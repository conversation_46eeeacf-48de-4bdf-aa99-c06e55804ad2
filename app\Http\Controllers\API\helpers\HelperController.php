<?php

namespace App\Http\Controllers\API\helpers;

use App\Models\City;
use App\Models\Role;
use App\Models\User;
use App\Models\Agency;
use App\Models\Country;
use App\Models\Product;
use App\Models\Personal;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\Cotisation;
use App\Models\Pack;
use App\Models\Payment;
use App\Models\Quarter;
use App\Models\Subscription;
use App\Models\Versement;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class HelperController extends Controller
{
    /**
     * apiResponse function
     * Send api response via json
     * @param array $response
     * @param integer $code
     * @return JsonResponse
     */
    public function apiResponse(array $response, $code = 200)
    {
        return response()->json($response, $code);
    }

    public function get_role_byID($role_id)
    {
        $role = Role::whereId($role_id)->first();
        return $role;
    }

    /**
     * get_role_byName function
     *
     * @param string $name
     * @return Role $role
     */
    public function get_role_byName(string $name)
    {
        $role = Role::where('name', $name)->first();
        return $role;
    }

    /**
     * check_exist_user function
     * Vérifier si un utilisateur existe déjà
     * @param string $name
     * @param string $phone
     * @param string|null $email
     * @return boolean $exist
     */
    public function check_exist_user(string $phone, string $email = null)
    {
        $res = User::where('phone', $phone)->first();
        $exist = false;
        if ($res !== null) {
            $exist = true;
        }
        return $exist;
    }

    /**
     * generate_username function
     * Générer un username unique
     * @param string $nom
     * @param string $prenoms
     * @return void
     */
    public function generate_username(string $nom, string $prenoms)
    {
        $username = "";
        $length = 6; // Longueur du username (à ajuster selon vos besoins)

        // Supprimer les espaces éventuels dans le nom et le prénom et les convertir en minuscules
        $nom = strtolower(str_replace(' ', '', $nom));
        $prenoms = strtolower(str_replace(' ', '', $prenoms));

        // Combinez le nom et le prénom pour former la base du username
        $base_username = $nom . $prenoms;

        // Ajouter un élément aléatoire supplémentaire pour rendre le username unique
        $random_element = Str::random($length - strlen($base_username));

        // Concaténer le nom, le prénom et l'élément aléatoire pour former le username final
        $username = $base_username . $random_element;

        return $username;
    }

    /**
     * get_auth_user function
     * @return User $user | null
     */
    public function get_auth_user(Request $request)
    {
        $user = null;
        $res = $request->user();
        if ($res !== null) {
            $user = $res;
        }
        return $user;
    }

    /**
     * remove_old_user_auth function
     *  @description: supprimer les anciens tokens d'authentification
     * @param integer $user_id
     * @return bool $is_trash
     */
    public function remove_old_user_auth(int $user_id)
    {
        $is_trash = false;
        $res = DB::table('personal_access_tokens')->where('tokenable_id', $user_id)->delete();
        if ($res !== null) {
            $is_trash = true;
        }
        return $is_trash;
    }

    /**
     * get_user_by_id function
     * Retourner un user par son id
     * @param integer $user_id
     * @return User $user
     */
    public function get_user_by_id(int $user_id)
    {
        $user = null;
        $res = User::where('id', $user_id)->with(['role', 'personal'])->first();
        if ($res !== null) {
            $user = $res;
        }
        return $user;
    }

    public function get_personal_by_user($user_id)
    {
        $res = Personal::where('user_id', $user_id)->first();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }

    /**
     * get_personal_by_id function
     * Retourner les informations personnelles d'un utilisateur par son id
     * @param integer $personal_id
     * @return Personal $res | null
     */
    public function get_personal_by_id(int $personal_id)
    {
        $res = Personal::where('id', $personal_id)->first();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }

    /**
     * get_personal_by_phone function
     * Retourner les informations personnelles d'un utilisateur par son numéro de téléphone
     * @param string $phone
     * @return Personal $res | null
     */
    public function get_personal_by_phone(string $phone)
    {
        $res = Personal::where('phone', $phone)->first();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }

    /**
     * get_personal_auth function
     * Retourner les informations personnelles de l'utilisateur authentifié
     * @param Request $request
     * @return Personal $res | null
     */
    public function get_personal_auth(Request $request)
    {
        $res = $request->user();
        if ($res !== null) {
            $user_id = $res->id;
            $res = $this->get_personal_by_user($user_id);
            if ($res !== null) {
                return $res;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    public function get_products()
    {
        $res = Product::with(['category', 'packs'])->orderBy('created_at', 'desc')->get();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }

    public function get_product_by_id(int $product_id)
    {
        $res = Product::where('id', $product_id)->with(['category', 'packs'])->first();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }

    public function get_product_byName(string $product_name)
    {
        $res = Product::where('name', $product_name)->with(['category', 'packs'])->first();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }

    /**
     * update_product_stock function
     * Mettre à jour le stock d'un produit
     * @param Product $product
     * @param integer $quantity
     * @param string $type
     * @return void
     */
    public function update_product_stock(Product $product, int $quantity, string $type)
    {
        $stock = $product->stock_quantity;
        if ($type === 'add') {
            $stock += $quantity;
        } else if ($type === 'remove') {
            if ($stock > $quantity) {
                $stock -= $quantity;
            }
        } else {
            $stock = $stock;
        }
        $product->stock_quantity = $stock;
        $product->save();
        return $product;
    }

    public function get_personals()
    {
        $res = Personal::with([
            'user',
            'quarter',
            'currentAgency',
            'city',
            'role',
            'currentAgency' => function ($query) {}
        ])->get();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }



    /**
     * get_personals_by_roleID function
     * Retourner les informations personnelles d'un utilisateur par son id
     * @param int $role_id
     * @return Personal $res
     */
    public function get_personals_by_roleID($role_id)
    {
        $res = Personal::where('role_id', $role_id)->with([
            'user',
            'quarter',
            'city',
            'role',
            'currentAgency',
        ])->get();
        return $res;
    }

    public function get_personal_by_role($role_id)
    {
        $role = Role::where('id', $role_id)->first();
        if ($role !== null) {
            $res = Personal::where('role_id', $role_id)->with(['user', 'agency', 'quarter'])->get();
            if ($res !== null) {
                return $res;
            } else {
                return null;
            }
        }
    }

    public function get_personal_by_agency($agency_id)
    {
        $res = Personal::where('agency_id', $agency_id)->with(['user', 'agency', 'quarter'])->get();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }

    public function get_personal_by_quarter($quarter_id)
    {
        $res = Personal::where('quarter_id', $quarter_id)->with(['user', 'agency', 'quarter'])->get();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }

    /**
     * get_all_clients function
     * Retourner les informations des clients
     * @return Client $res
     */
    public function get_all_clients()
    {
        $res = Client::orderBy('created_at', 'desc')->with([
            'user',
            'agency',
            'quarter',
            'city',
            'collectors',
            'principal_collector',
            'subscriptions',
            'cotisations'
        ])->get();
    }

    public function get_countries()
    {
        $res = Country::with(['cities'])->get();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }

    public function get_country_by_id($country_id)
    {
        $res = Country::where('id', $country_id)->first();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }

    public function get_cities()
    {
        $res = City::with(['country', 'quarters', 'agencies', 'clients'])->get();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }

    /**
     * get_agency function
     * Retourner les informations d'une agence par son id
     * @param integer $agency_id
     * @return Agency $res | null
     */
    public function get_agency(int $agency_id)
    {
        $res = Agency::where('id', $agency_id)->with(['city', 'personals', 'quarter', 'clients', 'subscriptions'])->first();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }

    public function get_collectors_by_agency(int $agency_id)
    {
        $res = Personal::where('agency_id', $agency_id)->with(['user', 'agency', 'quarter'])->get();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }

    public function get_clients_by_agency(int $agency_id)
    {
        $res = Client::where('agency_id', $agency_id)->with(['user', 'agency', 'quarter'])->get();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }

    /**
     * check_client_exist function
     * Vérifier si un client existe déjà dans la base de données
     * @param string $nom
     * @param string $prenom
     * @param string $telephone
     * @return Client $res | null
     */
    public function check_client_exist(string $nom, string $prenom, string $telephone)
    {
        $res = Client::where('nom', $nom)->where('prenoms', $prenom)->where('phone', $telephone)->first();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }

    public function get_subscriptions_by_agency(int $agency_id)
    {
        $clients = $this->get_clients_by_agency($agency_id);
        $subscriptions = [];
        foreach ($clients as $client) {
            $subs = Subscription::where('client_id', $client->id)->with(['client', 'pack'])->get();
            if ($subs !== null) {
                foreach ($subs as $sub) {
                    array_push($subscriptions, $sub);
                }
            }
        }
        $res = Subscription::where('agency_id', $agency_id)->with(['client', 'pack'])->get();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }

    public function get_quarters()
    {
        $res = Quarter::with(['city'])->get();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }

    /**
     * generate_code function
     * Générer un code unique pour les clients et les agences
     * @param string $name
     * @param string $type
     * @return string $shuffled_code
     */
    public function generate_code(string $name, string $type = 'AG')
    {
        $name = strtoupper($name);
        $code = $type . rand(1000, 9999);
        $shuffled_code = $code . str_shuffle(str_replace(' ', '', $name));
        $shuffled_code = htmlspecialchars($shuffled_code);
        $shuffled_code = substr(str_shuffle($shuffled_code), 0, 10); // Limiter à 10 caractères
        return $shuffled_code;
    }


    public function generate_productCode(string $name)
    {
        $name = strtoupper($name);
        $code = 'PRD' . rand(1000, 9999);
        $shuffled_code = $code . str_shuffle(str_replace(' ', '', $name));
        $shuffled_code = htmlspecialchars($shuffled_code);
        $shuffled_code = substr(str_shuffle($shuffled_code), 0, 10);
        return $shuffled_code;
    }

    public function generate_sub_code($nom, $prefix = 'CRT')
    {
        # get defaut prefix if .env file is not set
        $prefix = env('SUB_CODE_PREFIX', $prefix);
        $code = $prefix . '-' . strtoupper(substr($nom, 0, 3)) . '-' . str_pad(rand(0, 999999), 6, '0', STR_PAD_LEFT);
        return $code;
    }


    /**
     * get_client_byId function
     * Récupérer un client par son id
     * @param integer $client_id
     * @return Client $res | null
     */
    public function get_client_byId(int $client_id)
    {
        $res = Client::where('id', $client_id)->first();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }

    public function get_pack_byId(int $pack_id)
    {
        return Pack::with(['subscriptions', 'clients'])->find($pack_id);
    }

    public function get_cotisation_byId(int $cotisation_id)
    {
        $res = Cotisation::where('id', $cotisation_id)->with(['client', 'subscription', 'payments', 'collector'])->first();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }

    public function check_first_cotisation_by_client(int $client_id, int $subscription_id)
    {
        $res = Cotisation::where('client_id', $client_id)->where('subscription_id', $subscription_id)->first();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }

    public function get_payment_byId(int $payment_id)
    {
        $res = Payment::where('id', $payment_id)->with(['cotisation', 'client', 'collector'])->first();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }

    /**
     * check_client_pack_subscribe function
     *  Vérifier si un client est abonné à un pack
     * @param integer $client_id
     * @param integer $pack_id
     * @return void
     */
    public function check_client_pack_subscribe(int $client_id, int $pack_id)
    {
        $res = Subscription::where('client_id', $client_id)->where('pack_id', $pack_id)->first();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }

    /**
     * get_collector_activities_by_day function
     * Récupérer la somme des activités d'un collecteur par jour
     * @param Personal $collector
     * @return float $total_amount
     */
    public function get_collector_activities_by_day(Personal $collector)
    {
        // dd($collector);
        $total_amount = 0;
        $now = Carbon::now()->format('Y-m-d');
        $subscribes = $collector->payments()->whereDate('created_at', $now)->whereNotNull('subscription_id')->where('payment_type', 'CARNET')->whereNull('versement_id')->sum('amount');
        // dd($subscribes);
        $cotisations = $collector->payments()->whereDate('created_at', $now)->whereNotNull('cotisation_id')->where('payment_type', 'COLLECT')->whereNull('versement_id')->sum('amount');
        $total_amount = $subscribes + $cotisations;
        // dd($total_amount);

        return $total_amount;
    }

    /**
     * getCollectorUnpaidActivities function
     * Récupération des activités non payées du collecteur
     * @param Personal $collector
     * @return array $data[subscriptions,collects,total_amount]
     */
    public function getCollectorUnpaidActivities(Personal $collector)
    {
        $unpaidSubscriptions = $collector->payments()->whereNotNull('subscription_id')->where('payment_type', 'CARNET')->whereNull('versement_id')->get();
        $unpaidCollect = $collector->payments()->whereNotNull('cotisation_id')->where('payment_type', 'COLLECT')->whereNull('versement_id')->get();
        $total_amount = $unpaidSubscriptions->sum('carnet_price') + $unpaidCollect->sum('amount');

        return [
            'subscriptions' => $unpaidSubscriptions,
            'collects' => $unpaidCollect,
            'total_amount' => $total_amount
        ];
    }

    public function get_versement_by_Qrcode(string $qrcode)
    {
        $res = Versement::where('qrcode', $qrcode)->with(['collector'])->first();
        if ($res !== null) {
            return $res;
        } else {
            return null;
        }
    }

    public function get_personal_auth_agency(Request $request) {}

    /**
     * get_subscription_by_id function
     * Récupérer un abonnement par son id
     * @param integer $subscription_id
     * @return Subscription $subscribe
     */
    public function get_subscription_by_id(int $subscription_id)
    {
        $subscribe = null;
        $res = Subscription::where('id', $subscription_id)->with([
            'client',
            'pack',
            'cotisation',
            'payments'
        ])->first();

        if ($res !== null) {
            $subscribe = $res;
        }
        return $subscribe;
    }

    /**
     * get_global_collecte_sum function
     * Récupérer la somme des collectes
     * @return float $global_collect_sum
     */
    public function get_global_collecte_sum_waiting()
    {
        $collectors = Personal::where('role_id', 6)->with([
            'user',
        ])->get();
        $global_collect_sum = 0;
        foreach ($collectors as $key => $collector) {
            if ($collector->user_id !== null) {
                $user = $this->get_user_by_id($collector->user_id);
                $wallet = $user->wallet;
                if ($wallet !== null) {
                    $global_collect_sum += $wallet->balance;
                }
                # code...
            }
        }
        return $global_collect_sum;
    }

    public function get_subscriptions_year(int $year)
    {
        $res = Subscription::whereYear('created_at', $year)->with([
            'client',
            'pack',
            'collector',
            'payments',
            'agency',
            'cotisation'
        ])->where('status', 'pending')
            ->get();
        return $res;
    }

    public function get_lastest_cotisation_by_carnet(Subscription $sub)
    {
        $last_payment = Payment::where('subscription_id', $sub->id)

            ->whereNotNull('cotisation_id')->orderBy('payment_date', 'desc')->first();
        return $last_payment;
    }

    /**
     * get_carnets_inactif function
     * Récupérer les carnets inactifs d'au plus d'un mois de cotisation
     * @return array $data
     */
    public function get_carnets_inactif()
    {
        $current_year = intval(date('Y'));
        $subscribes = $this->get_subscriptions_year($current_year);
        $current_date = Carbon::now()->format('Y-m-d');
        $data = [];
        foreach ($subscribes as $key => $carnet) {
            $last_cotise = $this->get_lastest_cotisation_by_carnet($carnet);
            if ($last_cotise !== null) {
                $last_payment_date =  $last_cotise->created_at;
                $last_payment_date = Carbon::parse($last_payment_date)->format('Y-m-d');
                $date_diff = Carbon::parse($current_date)->diffInDays(Carbon::parse($last_payment_date));
                $percent = ($carnet->cotisation->total_amount / $carnet->pack->total_price) * 100;

                if ($date_diff > 5) {
                    $data[] = [
                        'last_payment_date' => $last_payment_date,
                        'carnet' => $carnet,
                        'date_diff' => $date_diff,
                        'percent' => number_format($percent, 2)
                    ];
                }
            }
        }
        return $data;
    }

    /**
     * get_carnets_inactif_by_date function
     * Récupérer les carnets inactifs entre 2 dates
     * @param string $date_start
     * @param string $date_end
     * @return array $data
     */
    public function get_carnets_inactif_by_date(string $date_start, string $date_end)
    {
        $date_start = Carbon::parse($date_start)->format('Y-m-d');
        $date_end = Carbon::parse($date_end)->format('Y-m-d');
        $year = Carbon::parse($date_start)->format('Y');
        $subscribes = $this->get_subscriptions_year($year);
        $data = [];
        foreach ($subscribes as $key => $carnet) {
            $last_cotise = $this->get_lastest_cotisation_by_carnet($carnet);
            if ($last_cotise !== null) {
                $last_payment_date = Carbon::parse($last_cotise->payment_date)->format('Y-m-d');
                $date_diff = Carbon::parse($date_end)->diffInDays(Carbon::parse($last_payment_date));
                if ($date_diff > 30) {
                    $data[] = $carnet;
                }
            }
        }
        return $data;
    }

    /**
     * check_key_month_exist function
     * Vérifier si le paiement d'une clé existe déjà pour un mois donné
     * @param integer $subscription_id
     * @param string $key
     * @param integer $month
     * @return bool $exist
     */
    public function check_key_month_exist(int $subscription_id, string $key, int $month)
    {
        $exist = false;
        $res = Payment::where('subscription_id', $subscription_id)->where('payment_type', 'COLLECT')->where('key', $key)->where('month', $month)->first();
        if ($res !== null) {
            $exist = true;
        }
        return $exist;
    }

    /**
     * check_month_payment_exist function
     * Vérifier si un paiement pour un mois existe déjà
     * @param int $subscription_id
     * @param int $month
     * @return bool $exist
     */
    public function check_month_payment_exist(int $subscription_id, int $month)
    {
        $exist = false;
        $res = Payment::where('subscription_id', $subscription_id)->where('payment_type', 'COLLECT')->where('month', $month)->first();
        if ($res !== null) {
            $exist = true;
        }
        return $exist;
    }

    /**
     * check_key_exist_on_month function
     * Vérifier si une clé existe déjà pour un mois donné
     * @param integer $subscription_id
     * @param string $key
     * @param integer $month
     * @return bool $exist
     */

    public function check_key_exist_on_month(int $subscription_id, string $key, int $month)
    {
        $exist = false;
        $res = Payment::where('subscription_id', $subscription_id)
            ->where('payment_type', 'COLLECT')
            ->where('month', $month)
            ->first();

        if ($res !== null) {
            $metadata = $res->metadata;
            // $metadataArray = json_decode($metadata, true);
            if (is_array($metadata)) {
                // Vérifier si la clé donnée existe dans l'un des objets du tableau
                foreach ($metadata as $entry) {
                    if (isset($entry['key']) && $entry['key'] == $key) {
                        $exist = true;
                        break;
                    }
                }
            }
        }

        return $exist;
    }

    /**
     * getMonthPayments function
     * Récupérer les paiements d'un mois
     * @param integer $subscription_id
     * @param integer $month
     * @return Payment $res
     */
    public function getMonthPayments(int $subscription_id, int $month)
    {
        $res = Payment::where('subscription_id', $subscription_id)
            ->where('payment_type', 'COLLECT')
            ->where('month', $month)
            ->first();
        return $res;
    }
}
