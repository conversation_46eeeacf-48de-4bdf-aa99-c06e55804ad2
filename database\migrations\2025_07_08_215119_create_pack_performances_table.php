<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('pack_performances')) {
            Schema::create('pack_performances', function (Blueprint $table) {
                $table->unsignedBigInteger('id')->primary(); // id du pack
                $table->string('pack_name');
                $table->decimal('carnet_price', 10, 2);
                $table->integer('total_subscriptions')->default(0);
                $table->integer('successful_subscriptions')->default(0);
                $table->decimal('subscription_revenue', 15, 2)->default(0);
                $table->integer('total_cotisations')->default(0);
                $table->integer('completed_cotisations')->default(0);
                $table->decimal('cotisation_revenue', 15, 2)->default(0);
                $table->decimal('performance_score', 10, 2)->default(0);
                $table->timestamps();

                $table->comment('Tableau de bord des performances des packs');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pack_performances');
    }
};
