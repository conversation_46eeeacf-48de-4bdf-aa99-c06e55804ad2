<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UsersTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = [
            [
                'username' => 'ROOT',
                'email' => '<EMAIL>',
                'phone' => '90909090',
                'password' => Hash::make('root'),
                'role_id' => 1,
                'is_online' => 1,
                'status' => 1,
                'secret_code' => '4321',
            ],
            [
                'username' => 'ADMIN',
                'email' => '<EMAIL>',
                'phone' => '90909091',
                'password' => Hash::make('admin'),
                'role_id' => 2,
                'is_online' => 1,
                'status'=>1,
                'secret_code'=>'4444',
            ],
        ];

        foreach ($users as $user) {
            \App\Models\User::create($user);
        }
    }
}
