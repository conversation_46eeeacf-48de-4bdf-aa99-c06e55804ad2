<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PackPerformance extends Model
{
    use HasFactory;

    protected $fillable = [
        'id',
        'pack_name',
        'carnet_price',
        'total_subscriptions',
        'successful_subscriptions',
        'subscription_revenue',
        'total_cotisations',
        'completed_cotisations',
        'cotisation_revenue',
        'performance_score',
        'last_updated'
    ];

    public $incrementing = false;

    public function pack()
    {
        return $this->belongsTo(Pack::class, 'id');
    }
}
