<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Agency extends Model
{
    use HasFactory;
    protected $fillable = [
        'name','address','city_id','phone','email','code','status','responsable_id','localisation','quarter_id',
        'perimeters','is_parent','parent_id',
    ];

    public function city()
    {
        return $this->belongsTo(City::class, 'city_id',);
    }

    public function quarter()
    {
        return $this->belongsTo(Quarter::class, 'quarter_id',);
    }

    public function clients()
    {
        return $this->hasMany(Client::class, 'agency_id');
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class, 'agency_id');
    }

    public function responsable()
    {
        return $this->belongsTo(Personal::class, 'responsable_id','id');
    }

    public function parent()
    {
        return $this->belongsTo(Agency::class, 'parent_id','id');
    }

    public function annexes()
    {
        return $this->hasMany(Agency::class, 'parent_id','id');
    }

    public function wallet(){
        return $this->hasOne(Wallet::class,'agency_id','id');
    }

    public function transactions()
    {
        return $this->hasMany(Transaction::class, 'agency_id', 'id');
    }

    /**
     * Get all of the versements for the Agency
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function versements(): HasMany
    {
        return $this->hasMany(Versement::class, 'agency_id', 'id');
    }

    /**
     * Get all of the cotisations for the Agency
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function cotisations(): HasMany
    {
        return $this->hasMany(Cotisation::class, 'agency_id', 'id');
    }

    /**
     * Get all of the controls for the Agency
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function controls(): HasMany
    {
        return $this->hasMany(Control::class, 'agency_id', 'id');
    }

    /**
     * Get all of the payments for the Agency
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class, 'agency_id', 'id');
    }

    /**
     * The personals that belong to the Agency
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function personals(): BelongsToMany
    {
        return $this->belongsToMany(Personal::class, 'agency_personals', 'agency_id', 'personal_id');
    }

    /**
     * currentPersonals function
     *  Get all of the current personals for the Agency
     * @return void
     */
    public function currentPersonals(){
        return $this->belongsToMany(Personal::class, 'agency_personals', 'agency_id', 'personal_id')
        ->wherePivot('is_current',1);
    }

    public function collectors(){
        return $this->belongsToMany(Personal::class, 'agency_personals', 'agency_id', 'personal_id')
        ->withPivot(['role_id','is_current','agency_id','personal_id','affected_at','mutated_at','created_at','updated_at','status'])
        ->wherePivot('role_id',6);
    }

    public function currentCollectors(){
        return $this->belongsToMany(Personal::class, 'agency_personals', 'agency_id', 'personal_id')
        ->withPivot(['role_id','is_current','agency_id','personal_id','affected_at','mutated_at','created_at','updated_at','status'])
        ->wherePivot('role_id',6)
        ->wherePivot('is_current',1)->withTimestamps();
    }

    public function cashiers(){
        return $this->belongsToMany(Personal::class, 'agency_personals', 'agency_id', 'personal_id')
        ->withPivot(['role_id','is_current','agency_id','personal_id','affected_at','mutated_at','created_at','updated_at','status'])
        ->wherePivot('role_id',5);
    }

    public function currentCashiers(){
        return $this->belongsToMany(Personal::class, 'agency_personals', 'agency_id', 'personal_id')
        ->withPivot(['role_id','is_current','agency_id','personal_id','affected_at','mutated_at','created_at','updated_at','status'])
        ->wherePivot('role_id',5)
        ->wherePivot('is_current',1);
    }

    public function supervisors(){
        return $this->belongsToMany(Personal::class, 'agency_personals', 'agency_id', 'personal_id')
        ->withPivot(['role_id','is_current','agency_id','personal_id','affected_at','mutated_at','created_at','updated_at','status'])
        ->wherePivot('role_id',4);
    }

    public function currentSupervisors(){
        return $this->belongsToMany(Personal::class, 'agency_personals', 'agency_id', 'personal_id')
        ->withPivot(['role_id','is_current','agency_id','personal_id','affected_at','mutated_at','created_at','updated_at','status'])
        ->wherePivot('role_id',4)
        ->wherePivot('is_current',1);
    }





}
