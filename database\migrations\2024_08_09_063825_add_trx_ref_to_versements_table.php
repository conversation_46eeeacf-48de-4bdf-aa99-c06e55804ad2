<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('versements', function (Blueprint $table) {
            $table->string('trx_ref')->nullable()->after('qrcode')->comment('Transaction reference');
            $table->enum('payment_mode',['CASH','TMONEY','FLOOZ','ECOBANK','MOBILEMONEY'])->default('CASH')->after('trx_ref');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('versements', function (Blueprint $table) {
            $table->dropColumn('trx_ref');
            $table->dropColumn('payment_mode');
        });
    }
};
