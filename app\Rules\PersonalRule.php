<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class PersonalRule implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // dd($value);
        $validate = $this->validate_data($value);
        if (!$validate) {
            $fail($this->message());
        }
    }

    public function message(): string
    {
        return 'Les données du personnels ne sont pas correcte !';
    }

    public function validate_data(array $data)
    {
        return isset($data['nom']) && isset($data['prenoms']) && array_key_exists('email', $data) &&
            isset($data['phone']) && isset($data['quarter_id']) && isset($data['role_id']) &&
            array_key_exists('agency_id', $data) && array_key_exists('lieu_nsce', $data) &&
            array_key_exists('date_nsce', $data);
    }
}
