<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clients', function (Blueprint $table) {
            $table->id();
            $table->string('nom');
            $table->string('prenoms');
            $table->string('email')->unique()->nullable();
            $table->string('phone')->unique()->nullable();
            $table->string('adresse')->nullable();
            $table->json('localisation')->nullable();
            $table->string('profession')->nullable();
            $table->integer('agency_id')->nullable();
            $table->integer('city_id')->nullable();
            $table->integer('quarter_id')->nullable();
            $table->integer('collector_id')->nullable()->comment("L'agent collecteur qui a crée le compte");
            $table->enum('status',['active','desactive'])->default('active');
            $table->integer('user_id')->nullable();
            $table->uuid('code')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clients');
    }
};
