<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('client_performances')) {
            Schema::create('client_performances', function (Blueprint $table) {
                $table->unsignedBigInteger('id')->primary(); // client.id
                $table->string('nom');
                $table->string('prenoms')->nullable();
                $table->string('phone')->nullable();
                $table->string('email')->nullable();

                $table->integer('finished_subscriptions_count')->default(0);
                $table->decimal('subscription_revenue', 15, 2)->default(0);

                $table->integer('finished_cotisations_count')->default(0);
                $table->decimal('cotisation_revenue', 15, 2)->default(0);

                $table->decimal('total_revenue', 15, 2)->default(0);
                $table->decimal('engagement_score', 10, 2)->default(0); // 0 à 100

                $table->timestamp('last_updated')->nullable();
                $table->timestamps();
                $table->comment('Tableau de bord des performances des clients');
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('client_performances');
    }
};
