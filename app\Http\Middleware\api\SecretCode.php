<?php

namespace App\Http\Middleware\api;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use Symfony\Component\HttpFoundation\Response;

class SecretCode
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        $secret_code = $request->secret_code;
        if ($user && Crypt::decryptString($user->secret_code) == $secret_code) {
            return $next($request);
        }else{
            return response()->json([
                'success'=>false,
                'message'=>'Veuillez entrer un code secret valide',
                'result'=>null
            ], 401);
        }
    }
}
