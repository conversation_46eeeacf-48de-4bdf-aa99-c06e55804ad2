<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Country extends Model
{
    use HasFactory;
    protected $fillable = [
        'name','prefix',
    ];

    public function cities()
    {
        return $this->hasMany(City::class, 'country_id', 'id');
    }

    /**
     * Get all of the agencies for the Country
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function agencies(): HasMany
    {
        return $this->hasMany(Agency::class, 'country_id', 'id');
    }
}
