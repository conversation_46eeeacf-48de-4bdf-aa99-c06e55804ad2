<?php

namespace App\Http\Controllers\API\admin;

use App\Http\Controllers\API\helpers\HelperController;
use App\Models\Pack;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PackController extends HelperController
{
    /**
     * get_all_packs function
     * get_all_packs
     * @param Request $request
     * @return void
     */
    public function get_all_packs(Request $request){
        $response = [];
        $status = 200;
        try {
            $page = $request->input('page');
            $limit = $request->input('limit');
            $packs = Pack::orderBy('id','desc')->withCount(['products','clients'])->paginate($limit,['*'],'page',$page);
            $response = [
                'success'=>true,
                'message'=>"Liste des packs",
                'result'=>$packs
            ];
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la récupération des packs",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    public function get_pack_products(Request $request){
        $response = [];
        $status = 200;
        try {
            $packId = $request->input('pack_id');
            $pack = Pack::where('id',$packId)->with(['products'])->first();
            $response = [
                'success'=>true,
                'message'=>"Liste des produits du pack",
                'result'=>$pack->products
            ];
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la récupération des produits du pack",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * add_pack function
     * add new pack
     * @param Request $request
     * @return void
     */
    public function add_pack(Request $request){
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(),[
                'name'=>'required',
                'tarif'=>'required|numeric',
                'total_price'=>'required',
                'products'=>'required|array',
                'products.*.product_id'=>'required|exists:products,id',
                'products.*.quantity'=>'required|integer',
                'products.*.price'=>'nullable',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success'=>false,
                    'message'=>"Echec de validation des données",
                    'result'=>null,
                    'errors'=>$validate->getMessageBag()
                ];
            } else {
                $check_pack_exist = Pack::where('name',$request->name)->first();
                if ($check_pack_exist != null) {
                    $response = [
                        'success'=>false,
                        'message'=>"Ce pack existe déjà",
                        'result'=>null,
                        'errors'=>null
                    ];
                } else {
                    $products = $request->products;
                    $pack = new Pack();
                    $pack->name = $request->name;
                    $pack->total_price = $request->tarif * 372;
                    $pack->description = $request->description;
                    $pack->category = $request->category;
                    $pack->tarif = $request->tarif;
                    $pack->save();
                    foreach ($products as $key => $product) {
                        if (isset($product['product_id']) && isset($product['quantity']) && key_exists('price',$product)) {
                            // $prod = $this->get_product_by_id($product['product_id']);
                            $pack->products()->attach($product['product_id'],[
                                'quantity'=>$product['quantity'],
                                'price'=>$product['price']*$product['quantity'],
                            ]);
                        }
                    }
                    $response = [
                        'success'=>true,
                        'message'=>"Pack ajouté avec succès",
                        'result'=>[
                            'pack'=>$pack,
                            'products'=>$pack->products
                        ]
                    ];
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de l'ajout du pack",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * get_pack_subscriptions function
     *  get all subscriptions of a pack
     * @param Request $request
     * @return void
     */
    public function get_pack_subscriptions(Request $request){
        $response = [];
        $status = 200;
        try {
            $data = [];
            $packs = Pack::orderBy('id','desc')->with(['products','clients'])->get();
            foreach ($packs as $key => $pack) {
                $data[] = [
                    'pack'=>$pack,
                    'subscriptions'=>$pack->clients,
                    'products'=>$pack->products,
                    'total_subscriptions'=>$pack->clients->count(),
                    'total_products'=>$pack->products->count(),
                ];
            }
            $response = [
                'success'=>true,
                'message'=>"Liste des packs",
                'result'=>$data
            ];
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la récupération des packs",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * get_details_pack function
     * get details of a pack
     * @param Request $request
     * @return void
     */
    public function get_details_pack(Request $request){
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(),[
                'pack_id'=>'required|exists:packs,id'
            ]);
            if ($validate->fails()) {
                $response = [
                    'success'=>false,
                    'message'=>"Echec de validation des données",
                    'result'=>null,
                    'errors'=>$validate->getMessageBag()
                ];
            } else {
                $pack = Pack::where('id',$request->pack_id)->with([
                    'products'=>function($query){
                        $query->with(['category']);
                    },
                    'clients'=>function($query){
                        $query->with(['client','pack','cotisation','payments','collector','agency']);
                    }
                ])->first();
                if ($pack == null) {
                    $response = [
                        'success'=>false,
                        'message'=>"Ce pack n'existe pas",
                        'result'=>null,
                        'errors'=>null
                    ];
                } else {
                    $response = [
                        'success'=>true,
                        'message'=>"Détails du pack",
                        'result'=>$pack
                    ];
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la récupération des détails du pack",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * update_pack function
     *  update a pack
     * @param Request $request
     * @return void
     */
    public function update_pack(Request $request){
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(),[
                'pack_id'=>'required|exists:packs,id',
                'name'=>'required',
                'tarif'=>'required|numeric',
                'total_price'=>'required',
                'products'=>'required|array',
                'products.*.product_id'=>'required|exists:products,id',
                'products.*.quantity'=>'required|integer',
                'products.*.price'=>'nullable',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success'=>false,
                    'message'=>"Echec de validation des données",
                    'result'=>null,
                    'errors'=>$validate->getMessageBag()
                ];
            } else {
                $pack = Pack::where('id',$request->pack_id)->first();
                if ($pack == null) {
                    $response = [
                        'success'=>false,
                        'message'=>"Ce pack n'existe pas",
                        'result'=>null,
                        'errors'=>null
                    ];
                } else {
                    $products = $request->products;
                    $pack->name = $request->name;
                    $pack->total_price = $request->total_price;
                    $pack->description = $request->description;
                    $pack->category = $request->category;
                    $pack->tarif = $request->tarif;
                    $pack->save();
                    $pack->products()->detach();
                    foreach ($products as $key => $product) {
                        if (isset($product['product_id']) && isset($product['quantity']) && isset($product['price'])) {
                            // $prod = $this->get_product_by_id($product['product_id']);
                            $pack->products()->attach($product['product_id'],[
                                'quantity'=>$product['quantity'],
                                'price'=>$product['price']*$product['quantity'],
                            ]);
                            // if ($prod != null) {
                            //     $this->update_product_stock($prod,$product['quantity'],'remove');
                            // }
                        }
                    }
                    $response = [
                        'success'=>true,
                        'message'=>"Pack modifié avec succès",
                        'result'=>[
                            'pack'=>$pack,
                            'products'=>$pack->products
                        ]
                    ];
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la modification du pack",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * delete_pack function
     *  delete a pack
     * @param Request $request
     * @return void
     */
    public function delete_pack(Request $request){
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(),[
                'pack_id'=>'required|exists:packs,id',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success'=>false,
                    'message'=>"Echec de validation des données",
                    'result'=>null,
                    'errors'=>$validate->getMessageBag()
                ];
            } else {
                $pack = Pack::where('id',$request->pack_id)->first();
                if ($pack == null) {
                    $response = [
                        'success'=>false,
                        'message'=>"Ce pack n'existe pas",
                        'result'=>null,
                        'errors'=>null
                    ];
                } else {
                    $pack->delete();
                    $response = [
                        'success'=>true,
                        'message'=>"Pack supprimé avec succès",
                        'result'=>null
                    ];
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la suppression du pack",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }
}

