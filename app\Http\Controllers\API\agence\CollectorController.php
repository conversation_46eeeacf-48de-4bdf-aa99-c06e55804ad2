<?php

namespace App\Http\Controllers\API\agence;

use App\Http\Controllers\API\agence\AgencyController;
use App\Http\Controllers\API\Traits\WalletTrait;
use App\Models\Client;
use App\Models\ClientCollector;
use App\Models\Pack;
use App\Models\Payment;
use App\Services\FirebaseService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class CollectorController extends AgencyController
{
    use WalletTrait;

    protected $firebaseService;

    public function __construct(FirebaseService $firebaseService)
    {
        $this->firebaseService = $firebaseService;
    }


    /**
     * get_clients function
     * Recupérer tous les clients ou rechercher le client d'un collecteur par son nom, prénoms, téléphone ou code
     * @param Request $request
     * @return JsonResponse $response
     */
    public function get_clients(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            // Récupération du collecteur (personnel authentifié)
            $personal = $this->get_personal_auth($request);

            if ($personal == null) {
                // Si l'utilisateur n'est pas autorisé
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => "Vous n'êtes pas autorisé à effectuer cette action",
                ];
            } else {
                // Récupération de l'agence actuelle
                $agency = $personal->currentAgency()->first();

                // Nombre de résultats par page
                $perPage = $request->input('limit', 10); // Par défaut, 10 résultats par page

                // Récupération des clients paginés
                $clients = $personal->clients()
                    ->wherePivot('agency_id', $agency->id)
                    ->where(function ($query) use ($request) {
                        $query->where('nom', 'like', '%' . $request->search . '%')
                            ->orWhere('prenoms', 'like', '%' . $request->search . '%')
                            ->orWhere('phone', 'like', '%' . $request->search . '%')
                            ->orWhere('code', 'like', '%' . $request->search . '%');
                    })
                    ->orderBy('created_at', 'desc')
                    ->with([
                        'city',
                        'quarter',
                        'agency',
                        'collectors',
                        'subscriptions' => function ($query) {
                            $query->with(['pack', 'cotisation' => function ($query) {
                                $query->with('payments')->orderBy('created_at', 'desc');
                            }])->orderBy('created_at', 'desc');
                        },
                        'packs' => function ($query) {
                            $query->orderBy('created_at', 'desc');
                        },
                    ])
                    ->paginate($perPage); // Pagination appliquée ici

                // Réponse avec succès et clients paginés
                $response = [
                    'success' => true,
                    'message' => "Liste des clients du collecteur",
                    'result' => [
                        'collector' => $personal,
                        'clients' => $clients, // Les résultats paginés
                    ],
                ];
            }
        } catch (\Throwable $th) {
            // Gestion des erreurs
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des clients du collecteur",
                'result' => null,
                'errors' => $th->getMessage(),
            ];
            $status = 500;
        }

        // Retour de la réponse API
        return $this->apiResponse($response, $status);
    }



    /**
     * get_all_packs function
     * get_all_packs
     * @param Request $request
     * @return void
     */
    public function get_all_packs(Request $request){
        $response = [];
        $status = 200;
        try {
            // Nombre de résultats par page
            // $perPage = $request->input('limit', 10); // Par défaut, 10 résultats par page

            // $packs = Pack::orderBy('id','desc')->with(['products', 'clients'])->paginate($perPage);
            $packs = Pack::orderBy('id', 'desc')->with(['products', 'clients'])->get();

            $response = [
                'success'=>true,
                'message'=>"Liste des packs",
                'result'=>$packs
            ];
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la récupération des packs",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * add_client function
     *  Ajout d'un client par un collecteur
     * @param Request $request
     * @return void
     */
    public function add_client(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                // 'collector_id'=>'required|exists:personals,id',
                'nom' => 'required',
                'prenoms' => 'required',
                'phone' => 'required',
                'profession' => 'required',
                'city_id' => 'required|exists:cities,id',
                'quarter_id' => 'required|exists:quarters,id',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag(),
                ];
            } else {
                $nom = strtoupper($request->nom);
                $prenoms = $request->prenoms;
                $phone = $request->phone;
                $localisation = $request->localisation;
                $collector = $this->get_personal_auth($request);
                $agency_id = $collector->currentAgency()->first()->id;
                $check_client = $this->check_client_exist($nom, $prenoms, $phone);
                // dd($check_client);
                // $client = null;
                if ($check_client !== null) {
                    $is_attach = ClientCollector::where('client_id', $check_client->id)->where('collector_id', $collector->id)->where('agency_id', $agency_id)->first();
                    if ($is_attach == null) {
                        $collector->clients()->attach($check_client->id, [
                            'agency_id' => $agency_id,
                        ]);
                        $data_client = $check_client->with([
                            'city',
                            'quarter',
                            'agency',
                            'collectors',
                            'packs',
                            'subscriptions' => function ($query) {
                                $query->with(['cotisation' => function ($query) {
                                    $query->with('payments');
                                }]);
                            },
                        ])->whereId($check_client->id)->first();
                        $response = [
                            'success' => true,
                            'message' => "Client ajouté avec succès",
                            'result' => $data_client,
                        ];
                    } else {
                        $data_client = $check_client->with([
                            'city',
                            'quarter',
                            'agency',
                            'collectors',
                            'packs',
                            'subscriptions' => function ($query) {
                                $query->with(['cotisation' => function ($query) {
                                    $query->with('payments');
                                }]);
                            },
                        ])->whereId($check_client->id)->first();

                        $response = [
                            'success' => false,
                            'message' => "Client ajouté avec succès",
                            'result' => $data_client,
                            'errors' => null,
                            'except' => "Ce client est déjà attaché à ce collecteur",
                        ];
                    }
                } else {
                    $client = Client::create([
                        'nom' => strtoupper($request->nom),
                        'prenoms' => $request->prenoms,
                        'phone' => $request->phone,
                        'profession' => $request->profession,
                        'city_id' => $request->city_id,
                        'quarter_id' => $request->quarter_id,
                        'agency_id' => $agency_id,
                        'code' => $this->generate_code($request->nom, 'CL'),
                        'localisation' => $localisation,
                    ]);
                    // dd($client);
                    $client->collectors()->attach($collector->id, [
                        'agency_id' => $agency_id,
                        'is_principal' => true,
                    ]);
                    // $client_res = $check_client != null ? $check_client : $client;
                    $data_client = $client->with([
                        'city',
                        'quarter',
                        'agency',
                        'collectors',
                        'packs',
                        'subscriptions' => function ($query) {
                            $query->with(['cotisation' => function ($query) {
                                $query->with('payments');
                            }]);
                        },
                    ])->whereId($client->id)->first();
                    $response = [
                        'success' => true,
                        'message' => "Client ajouté avec succès",
                        'result' => $data_client,
                    ];
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de l'ajout du client",
                'result' => null,
                'errors' => $th->getMessage(),
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * get_client_details function
     * Récupération des détails d'un client
     * @param Request $request
     * @return JsonResponse $response
     */
    public function get_client_details(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'client_id' => 'required|exists:clients,id',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag(),
                ];
            } else {
                $collector = $this->get_personal_auth($request);
                if ($collector == null) {
                    $response = [
                        'success' => false,
                        'message' => "Echec de validation des données",
                        'result' => null,
                        'errors' => "Vous n'êtes pas autorisé à effectuer cette action",
                    ];
                } else {
                    $client = $this->get_client_byId($request->client_id);
                    $details = $client->with([
                        'city',
                        'quarter',
                        'agency',
                        'collectors',
                        'packs',
                        'collector',
                        'packs' => function ($query) {
                            $query->with('products')->orderBy('created_at', 'desc');
                        },
                        'cotisations' => function ($query) {
                            $query->with(['subscription', 'collector', 'payments']);
                        },
                        'subscriptions' => function ($query) {
                            $query->with(['pack', 'cotisation' => function ($query) {
                                $query->with('payments')->orderBy('created_at', 'desc');
                            }])->orderBy('created_at', 'desc');
                        },
                    ])->whereId($client->id)->first();
                    $response = [
                        'success' => true,
                        'message' => "Détails du client",
                        'result' => [
                            'client' => $client,
                            'details' => $details,
                        ],
                    ];
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération du client",
                'result' => null,
                'errors' => $th->getMessage(),
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * search_client function
     * Recherche d'un client d'un collecteur d'une agence par son nom, prénoms, téléphone ou code
     * @param Request $request
     * @return JsonResponse $response
     */
    public function search_client(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'search' => 'required',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag(),
                ];
            } else {
                $collector = $this->get_personal_auth($request);
                $agency = $collector->currentAgency()->first();
                // Nombre de résultats par page
                $perPage = $request->input('limit', 10); // Par défaut, 10 résultats par page
                $clients = $collector->clients()->wherePivot('agency_id', $agency->id)->where(function ($query) use ($request) {
                        $query->where('nom', 'like', '%' . $request->search . '%')
                            ->orWhere('prenoms', 'like', '%' . $request->search . '%')
                            ->orWhere('phone', 'like', '%' . $request->search . '%')
                            ->orWhere('code', 'like', '%' . $request->search . '%');
                    })->orderBy('created_at', 'desc')->with([
                    'city',
                    'quarter',
                    'agency',
                    'collectors',
                    'subscriptions' => function ($query) {
                        $query->with(['pack', 'cotisation' => function ($query) {
                            $query->with('payments')->orderBy('created_at', 'desc');
                        }])->orderBy('created_at', 'desc');
                    },
                    'packs' => function ($query) {
                        $query->orderBy('created_at', 'desc');
                    },
                ])->paginate($perPage);

                $response = [
                    'success' => true,
                    'message' => "Résultats de la recherche",
                    'result' => $clients,
                ];
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la recherche",
                'result' => null,
                'errors' => $th->getMessage(),
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * global_search_client function
     * Recherche d'un client des agences par son nom, prénoms, téléphone ou code
     * @param Request $request
     * @return JsonResponse $response
     */
    public function global_search_client(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'search' => 'required',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag(),
                ];
            } else {
                $clients = Client::where('nom', 'like', '%' . $request->search . '%')
                    ->orWhere('prenoms', 'like', '%' . $request->search . '%')->orWhere('phone', 'like', '%' . $request->search . '%')
                    ->orWhere('code', 'like', '%' . $request->search . '%')->orderBy('created_at', 'desc')->with([
                    'city',
                    'quarter',
                    'agency',
                    'collectors',
                    'subscriptions' => function ($query) {
                        $query->with(['pack', 'cotisation' => function ($query) {
                            $query->with('payments')->orderBy('created_at', 'desc');
                        }])->orderBy('created_at', 'desc');
                    },
                    'packs' => function ($query) {
                        $query->orderBy('created_at', 'desc');
                    },
                ])->get();

                $response = [
                    'success' => true,
                    'message' => "Résultats de la recherche",
                    'result' => $clients,
                ];
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la recherche",
                'result' => null,
                'errors' => $th->getMessage(),
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * add_client_subscription function
     * Ajout d'un abonnement à un client
     * @param Request $request
     * @return JsonResponse $response
     */
    public function add_client_subscription(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'client_id' => 'required|exists:clients,id',
                'pack_id' => 'required|exists:packs,id',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag(),
                ];
            } else {
                $collector = $this->get_personal_auth($request);
                $agency = $collector->currentAgency()->first();
                $client = $this->get_client_byId($request->client_id);
                $pack = Pack::where('id', $request->pack_id)->first();
                // dd($pack);
                $description = "Abonnement au pack " . $pack->name . " pour une durée de " . $pack->duration . " jours";
                // dd($description);
                $code = $this->generate_sub_code($client->nom, 'BEN');
                $subscription = $client->subscriptions()->create([
                    'pack_id' => $pack->id,
                    'started_at' => Carbon::now(),
                    'finished_at' => Carbon::now()->addDays($pack->duration)->addDays(-1),
                    'price' => $pack->tarif, // mise
                    'carnet_price' => $pack->carnet_price,
                    'code' => mb_convert_encoding($code, 'UTF-8', mb_list_encodings()), // Ensure UTF-8 encoding
                    'motif' => $request->motif,
                    'agency_id' => $agency->id,
                    'collector_id' => $collector->id,
                ]);
                //add payment
                $payment = $subscription->payments()->create([
                    'amount' => $pack->carnet_price,
                    'payment_date' => Carbon::now(),
                    'payment_mode' => 'CASH',
                    'collector_id' => $collector->id,
                    'agency_id' => $client->agency_id,
                    'code' => Str::uuid(),
                    'description' => $description,
                    'pack_id' => $pack->id,
                    'client_id' => $client->id,
                    'status' => "completed",
                    'payment_type' => 'CARNET',
                ]);
                //credit collector wallet
                $wallet = $collector->user->wallet;
                $credite = $this->credite_wallet($wallet, $pack->carnet_price);
                $details = $client->with([
                    'city',
                    'quarter',
                    'agency',
                    'collectors',
                    'packs',
                    'cotisations',
                    'collector',
                    'packs' => function ($query) {
                        $query->with('products')->orderBy('created_at', 'desc');
                    },
                    'cotisations' => function ($query) {
                        $query->with(['subscription', 'collector', 'payments', 'agency']);
                    },
                    'subscriptions' => function ($query) {
                        $query->with(['pack', 'cotisation' => function ($query) {
                            $query->with('payments')->orderBy('created_at', 'desc');
                        }])->orderBy('created_at', 'desc');
                    },
                ])->whereId($client->id)->first();
                $response = [
                    'success' => true,
                    'message' => "Abonnement ajouté avec succès",
                    'result' => [
                        'client' => $details,
                        'subscription' => $subscription,
                        'payment' => $payment,
                        'credite' => $credite,
                    ],
                ];

                // send push notification
                $title = "Vente de carnet";
                $body = "Vous venez de vendre le carnet {$pack->name} de {$subscription->carnet_price} FCFA au client {$client->nom} {$client->prenoms}";
                if ($collector->user->device_token !== null) {
                    $this->firebaseService->sendNotification($collector->user->device_token, $title, $body);
                }

                // send push notification to other active and connected collectors and admins
                $active_connected_admins_collectors_users_device_tokens = $this->get_active_connected_users_device_tokens_by_roles([1, 2, 3], [$collector->user->id]);
                if ($active_connected_admins_collectors_users_device_tokens['success'] == true) {
                    $tokens = $active_connected_admins_collectors_users_device_tokens['result'];
                    if (count($tokens) > 0) {
                        $body = "Le collecteur {$collector->user->username} viens de vendre un carnet {$pack->name} de {$subscription->carnet_price} FCFA au client {$client->nom} {$client->prenoms}";
                        $this->firebaseService->sendNotifications($tokens, $title, $body);
                    }
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de l'ajout de l'abonnement",
                'result' => null,
                'errors' => $th->getMessage(),
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * get_collector_details function
     * Récupération des détails d'un collecteur
     * @param Request $request
     * @return JsonResponse $response
     */
    public function get_collector_details(Request $request)
    {
        $response = [];
        $status = 200;

        try {
            $collector = $this->get_personal_auth($request);

            // Récupérer l'agence courante et vérifier son existence
            $agency = $collector->currentAgency()->first();
            if (!$agency) {
                throw new \Exception("Aucune agence courante associée au collecteur.");
            }

            // Charger les relations nécessaires
            $collector->load([
                'clients' => function($query) use ($agency) {
                    $query->wherePivot('agency_id', $agency->id);
                },
                'subscriptions' => function($query) use ($agency) {
                    $query->where('agency_id', $agency->id);
                },
                'payments' => function($query) use ($agency) {
                    $query->where('agency_id', $agency->id);
                },
                'cotisations' => function($query) use ($agency) {
                    $query->where('agency_id', $agency->id);
                },
                'user.wallet',
                'user.role'
            ]);

            // Calculer la somme de `nbre_cotise` pour les cotisations et le nombre de souscriptions du collecteur
            $totalNbreCotise = $collector->cotisations->sum('nbre_cotise');
            $totalSubscriptions = $collector->subscriptions->count();
            $totalClients = $collector->clients->count();

            // Rassembler les données
            $data = [
                'collector' => $collector,
                'agency' => $agency,
                'city' => $collector->city,
                'quarter' => $collector->quarter,
                'clients' => $collector->clients,
                'subscriptions' => $collector->subscriptions,
                'payments' => $collector->payments,
                'cotisations' => $collector->cotisations,
                'total_nbre_cotise' => $totalNbreCotise, // Somme de nbre_cotise
                'total_subscriptions' => $totalSubscriptions, // Nombre total de souscriptions
                'total_clients' => $totalClients, // Nombre total de clients
                'user' => $collector->user,
                'role' => $collector->user->role,
                'wallet' => $collector->user->wallet,
            ];

            $response = [
                'success' => true,
                'message' => "Détails du collecteur",
                'result' => $data,
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Échec de la récupération du collecteur",
                'result' => null,
                'errors' => $th->getMessage(),
            ];
            $status = 500;
        }

        return $this->apiResponse($response, $status);
    }


    /**
     * get_collector_profile function
     * Récupération du profil d'un collecteur
     * @param Request $request
     * @return JsonResponse $response
     */
    public function get_collector_profile(Request $request)
    {
        $response = [];
        $status = 200;

        try {
            $collector = $this->get_personal_auth($request);

            // Récupérer l'agence courante et vérifier son existence
            $agency = $collector->currentAgency()->first();
            if (!$agency) {
                throw new \Exception("Aucune agence courante associée au collecteur.");
            }

            // Calculer la somme de `nbre_cotise` pour les cotisations et le nombre de souscriptions du collecteur
            $totalNbreCotise = $collector->cotisations->sum('nbre_cotise');
            $totalSubscriptions = $collector->subscriptions->count();
            $totalClients = $collector->clients->count();

            // Rassembler les données
            $data = [
                'collector' => $collector,
                'agency' => $agency,
                'quarter' => $collector->quarter,
                'total_nbre_cotise' => $totalNbreCotise, // Somme de nbre_cotise
                'total_subscriptions' => $totalSubscriptions, // Nombre total de souscriptions
                'total_clients' => $totalClients, // Nombre total de clients
                'user' => $collector->user,
            ];

            $response = [
                'success' => true,
                'message' => "Détails du collecteur",
                'result' => $data,
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Échec de la récupération du collecteur",
                'result' => null,
                'errors' => $th->getMessage(),
            ];
            $status = 500;
        }

        return $this->apiResponse($response, $status);
    }


    /**
     * add_cotisation_withKeys function
     * Ajout d'une cotisation avec les clés
     * @param Request $request
     * @return JsonResponse $response
     */
    public function add_cotisation_withKeys(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'client_id' => 'required|exists:clients,id',
                'amount' => 'required|numeric',
                'subscription_id' => 'required|exists:subscriptions,id',
                'mise' => 'required|numeric',
                'nbre_cotise' => 'required|numeric',
                'payments' => 'required|array',
                'payments.*.amount' => 'required|numeric',
                'payments.*.month' => 'required|numeric',
                'payments.*.key' => 'required|numeric',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag(),
                ];
            } else {
                $amount = $request->amount;
                $collector = $this->get_personal_auth($request);
                $agency = $collector->currentAgency()->first();
                $client = $this->get_client_byId($request->client_id);
                $subscription = $client->subscriptions()->where('id', $request->subscription_id)->first();
                if ($subscription == null) {
                    $response = [
                        'success' => false,
                        'message' => "Ce client n'est pas abonné à ce pack",
                        'result' => [
                            'client' => $client,
                            'subscription' => $subscription,
                        ],
                        'errors' => null,
                    ];
                } else {
                    // check if subscription status in finised
                    if ($subscription->status == "finished") {
                        $response = [
                            'success' => false,
                            'message' => "La cotisation pour ce carnet produit est dejà arrivée à son terme.",
                            'result' => [
                                'subscription' => $subscription,
                                'mode' => $request->mode,
                            ],
                        ];
                    } else {
                        $check_first = $this->check_first_cotisation_by_client($client->id, $request->subscription_id);
                        $tarif = $client->subscriptions()->where('id', $request->subscription_id)->first()->pack->tarif;
                        // $nbre_cotise = $request->nbre_cotise ?? ceil($request->amount/$tarif);
                        $nbre_cotise = (isset($request->nbre_cotise)) ? $request->nbre_cotise : ceil($request->amount / $tarif);
                        // dd($check_first);
                        if ($check_first == null) {
                            $now = Carbon::now()->format('Y-m-d');
                            $cotisation = $client->cotisations()->create([
                                'date_cotise' => $now,
                                'nbre_cotise' => $nbre_cotise,
                                'total_amount' => $amount,
                                'collector_id' => $collector->id,
                                'subscription_id' => $request->subscription_id,
                                'description' => "Première cotisation {$now}",
                                'agency_id' => $agency->id,
                                'start_at' => Carbon::now()->format('Y-m-d H:i:s'),
                            ]);
                            $subscription->status = $nbre_cotise == 372 ? "finished" : "started";
                            $subscription->started_at = Carbon::now()->format('Y-m-d');
                            $subscription->finished_at = Carbon::now()->addDays($subscription->pack->duration)->addDay(-1);
                            $subscription->save();
                            //add payment for amount paid
                            $payments = $request->payments;
                            $payment_data = [];
                            foreach ($payments as $key => $payment) {
                                //check if payment already exist
                                $exist = $this->check_key_month_exist($request->subscription_id, $payment['key'], $payment['month']);
                                if ($exist === false) {
                                    # add payment
                                    $payment_date = Carbon::now()->format('Y-m-d H:i:s');
                                    $description = "Cotisation du {$payment_date} ";
                                    $payment_data[] = $cotisation->payments()->create([
                                        'amount' => $payment['amount'],
                                        'description' => $description,
                                        'collector_id' => $collector->id,
                                        'subscription_id' => $request->subscription_id,
                                        'pack_id' => $subscription->pack_id,
                                        'payment_date' => $payment_date,
                                        'payment_mode' => 'CASH',
                                        'agency_id' => $agency->id,
                                        'client_id' => $client->id,
                                        'status' => "completed",
                                        'key' => $payment['key'],
                                        'month' => $payment['month'],
                                        'payment_type' => 'COLLECT',
                                    ]);
                                }

                            }
                            //credit collector wallet
                            $wallet = $collector->user->wallet;
                            $credite = $this->credite_wallet($wallet, $request->amount);

                            $response = [
                                'success' => true,
                                'message' => "Cotisation ajoutée avec succès",
                                'result' => [
                                    'cotisation' => $cotisation,
                                    'payments' => $payment_data,
                                    'credite' => $credite,
                                ],
                            ];
                        } else {
                            //cotisation existante
                            $cotisation = $client->cotisations()->where('subscription_id', $request->subscription_id)->first();
                            $old_nbre_cotise = $cotisation->nbre_cotise;
                            if ($old_nbre_cotise >= 372) {
                                $response = [
                                    'success' => false,
                                    'message' => "Le nombre de cotisation {$old_nbre_cotise} pour ce carnet est atteins",
                                    'result' => [
                                        'cotisation' => $cotisation,
                                        'mode' => $request->mode,
                                    ],
                                ];
                                return $this->apiResponse($response, $status);
                            } else {
                                //update cotisation
                                $total_amount = $cotisation->total_amount + $request->amount;
                                $up_nbre_cotise = $old_nbre_cotise + $nbre_cotise;
                                $real_total_amount = $tarif * 372;
                                $now = Carbon::now()->format('Y-m-d');
                                $end_at = ($total_amount >= $real_total_amount) ? Carbon::now()->format('Y-m-d H:i:s') : null;
                                $cotisation->update([
                                    'date_cotise' => $now,
                                    'description' => "Cotisation du {$now}",
                                    'nbre_cotise' => $up_nbre_cotise,
                                    'total_amount' => $total_amount,
                                    'collector_id' => $collector->id,
                                    'subscription_id' => $request->subscription_id,
                                    'end_at' => $end_at,
                                    'status' => 'in_progress',
                                ]);
                                if ($end_at !== null) {
                                    $subscription->status = "finished";
                                    $subscription->save();
                                    $cotisation->update([
                                        'status' => "finished",
                                    ]);
                                } else {
                                    $subscription->status = "pending";
                                    $subscription->save();
                                }
                                //add payment for amount paid
                                $payments = $request->payments;
                                $payment_data = [];
                                foreach ($payments as $key => $payment) {
                                    $exist = $this->check_key_month_exist($request->subscription_id, $payment['key'], $payment['month']);
                                    # add payment
                                    if ($exist === false) {
                                        # code...
                                        $payment_date = Carbon::now()->format('Y-m-d H:i:s');
                                        $description = "Cotisation du {$payment_date} ";
                                        $payment_data[] = $cotisation->payments()->create([
                                            'amount' => $payment['amount'],
                                            'description' => $description,
                                            'collector_id' => $collector->id,
                                            'subscription_id' => $request->subscription_id,
                                            'pack_id' => $subscription->pack_id,
                                            'payment_date' => $payment_date,
                                            'payment_mode' => 'CASH',
                                            'agency_id' => $agency->id,
                                            'client_id' => $client->id,
                                            'status' => "completed",
                                            'key' => $payment['key'],
                                            'month' => $payment['month'],
                                            'payment_type' => 'COLLECT',
                                        ]);
                                    }
                                }
                                //credit collector wallet
                                $wallet = $collector->user->wallet;
                                $credite = $this->credite_wallet($wallet, $request->amount);
                                $response = [
                                    'success' => true,
                                    'message' => "Cotisation ajoutée avec succès",
                                    'result' => [
                                        'cotisation' => $cotisation,
                                        'payments' => $payment_data,
                                        'credite' => $credite,
                                    ],
                                ];

                                // send push notification
                                $title = "Cotisation";
                                $cotisation_text = $nbre_cotise > 1 ? "Cotisations" : "Cotisation";
                                $body = "Vous venez d'effectuer {$nbre_cotise} {$cotisation_text} de {$request->amount} FCFA dans le carnet {$subscription->code} du client {$client->nom} {$client->prenoms}";
                                if ($collector->user->device_token !== null) {
                                    $this->firebaseService->sendNotification($collector->user->device_token, $title, $body);
                                }

                                // send push notification to  admins and others active and connected collectors
                                $active_connected_admins_collectors_users_device_tokens = $this->get_active_connected_users_device_tokens_by_roles([1, 2, 3], [$collector->user->id]);
                                if ($active_connected_admins_collectors_users_device_tokens['success'] == true) {
                                    $tokens = $active_connected_admins_collectors_users_device_tokens['result'];
                                    if (count($tokens) > 0) {
                                        $body = "Le collecteur {$collector->user->username} viens d'effectuer {$nbre_cotise} {$cotisation_text} de {$request->amount} FCFA dans le carnet {$subscription->pack->name} du client {$client->nom} {$client->prenoms}";
                                        $this->firebaseService->sendNotifications($tokens, $title, $body);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de l'ajout de la cotisation",
                'result' => null,
                'errors' => $th->getMessage(),
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function addcCtisationWithKeysV2(Request $request)
    {
        $response = [];
        $status = 200;
        DB::beginTransaction();
        try {
            $validate = Validator::make($request->all(), [
                'client_id' => 'required|exists:clients,id',
                'amount' => 'required|numeric',
                'subscription_id' => 'required|exists:subscriptions,id',
                'mise' => 'required|numeric',
                'nbre_cotise' => 'required|numeric',
                'payments' => 'required|array',
                'payments.*.amount' => 'required|numeric',
                'payments.*.month' => 'required|numeric',
                'payments.*.key' => 'required|numeric',
                'month' => 'required|numeric',
                'payments.*.payment_date' => 'required|date',
                'payments.*.id' => 'nullable|string',
                'payments.*.status' => 'nullable|string',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag(),
                ];
            } else {
                $amount = $request->amount;
                $collector = $this->get_personal_auth($request);
                $agency = $collector->currentAgency()->first();
                $client = $this->get_client_byId($request->client_id);
                $subscription = $client->subscriptions()->where('id', $request->subscription_id)->first();
                if ($subscription == null) {
                    $response = [
                        'success' => false,
                        'message' => "Ce client n'est pas abonné à ce pack",
                        'result' => [
                            'client' => $client,
                            'subscription' => $subscription,
                        ],
                        'errors' => null,
                    ];
                } else {
                    // check if subscription status in finised
                    if ($subscription->status == "finished") {
                        $response = [
                            'success' => false,
                            'message' => "La cotisation pour ce carnet produit est dejà arrivée à son terme.",
                            'result' => [
                                'subscription' => $subscription,
                                'mode' => $request->mode,
                            ],
                        ];
                    } else {
                        $check_first = $this->check_first_cotisation_by_client($client->id, $request->subscription_id);
                        $tarif = $client->subscriptions()->where('id', $request->subscription_id)->first()->pack->tarif;
                        // $nbre_cotise = $request->nbre_cotise ?? ceil($request->amount/$tarif);
                        $nbre_cotise = (isset($request->nbre_cotise)) ? $request->nbre_cotise : ceil($request->amount / $tarif);
                        // dd($check_first);
                        if ($check_first == null) {
                            $now = Carbon::now()->format('Y-m-d');
                            $cotisation = $client->cotisations()->create([
                                'date_cotise' => $now,
                                'nbre_cotise' => $nbre_cotise,
                                'total_amount' => $amount,
                                'collector_id' => $collector->id,
                                'subscription_id' => $request->subscription_id,
                                'description' => "Première cotisation {$now}",
                                'agency_id' => $agency->id,
                                'start_at' => Carbon::now()->format('Y-m-d H:i:s'),
                            ]);
                            $subscription->status = $nbre_cotise == 372 ? "finished" : "started";
                            $subscription->started_at = Carbon::now()->format('Y-m-d');
                            $subscription->finished_at = Carbon::now()->addDays($subscription->pack->duration)->addDay(-1);
                            $subscription->save();

                            //create first payment for month
                            $resPayment = null;
                            $checkMonth = $this->check_month_payment_exist($request->subscription_id, $request->month);
                            if ($checkMonth === false) {
                                $metadata = $request->payments;
                                $metadata = array_map(function ($item, int $index) {
                                    return [
                                        'id' => Str::uuid(),
                                        'key' => $item['key'],
                                        'month' => $item['month'],
                                        'amount' => $item['amount'],
                                        'payment_date' => $item['payment_date'] ?? Carbon::now()->format('Y-m-d'),
                                        'status' => $item['status'] ?? 'completed',
                                    ];
                                }, $metadata, array_keys($metadata));
                                $resPayment[] = $cotisation->payments()->create([
                                    'amount' => $amount,
                                    'description' => "Première cotisation du {$now}",
                                    'pack_id' => $subscription->pack_id,
                                    'collector_id' => $collector->id,
                                    'subscription_id' => $request->subscription_id,
                                    'payment_date' => Carbon::now()->format('Y-m-d H:i:s'),
                                    'payment_mode' => 'CASH',
                                    'agency_id' => $agency->id,
                                    'client_id' => $client->id,
                                    'status' => $request->nbre_cotise == 31 ? "completed" : "pending",
                                    'payment_type' => 'COLLECT',
                                    'month' => $request->month,
                                    'metadata' => $metadata,
                                ]);
                            }
                            //credit collector wallet
                            $wallet = $collector->user->wallet;
                            $credite = $this->credite_wallet($wallet, $request->amount);

                            if ($credite['success'] == true) {
                                DB::commit();
                                $response = [
                                    'success' => true,
                                    'message' => "Cotisation ajoutée avec succès",
                                    'result' => [
                                        'cotisation' => $cotisation,
                                        'payments' => $resPayment,
                                        'credite' => $credite,
                                    ],
                                ];
                                // send push notification
                                $title = "Cotisation";
                                $cotisation_text = $nbre_cotise > 1 ? "Cotisations" : "Cotisation";
                                $body = "Vous venez d'effectuer {$nbre_cotise} {$cotisation_text} de {$request->amount} FCFA dans le carnet {$subscription->pack->name} du client {$client->nom} {$client->prenoms}";
                                if ($collector->user->device_token !== null) {
                                    $this->firebaseService->sendNotification($collector->user->device_token, $title, $body);
                                }

                                // send push notification to  admins and others active and connected collectors
                                $active_connected_admins_collectors_users_device_tokens = $this->get_active_connected_users_device_tokens_by_roles([1, 2, 3], [$collector->user->id]);
                                if ($active_connected_admins_collectors_users_device_tokens['success'] == true) {
                                    $tokens = $active_connected_admins_collectors_users_device_tokens['result'];
                                    if (count($tokens) > 0) {
                                        $body = "Le collecteur {$collector->user->username} viens d'effectuer {$nbre_cotise} {$cotisation_text} de {$request->amount} FCFA dans le carnet {$subscription->pack->name} du client {$client->nom} {$client->prenoms}";
                                        $this->firebaseService->sendNotifications($tokens, $title, $body);
                                    }
                                }
                            } else {
                                DB::rollback();
                                $response = [
                                    'success' => false,
                                    'message' => "Echec d'ajout de la cotisation",
                                    'result' => null,
                                    'except' => $credite,
                                ];
                            }
                        } else {
                            //cotisation existante
                            $cotisation = $client->cotisations()->where('subscription_id', $request->subscription_id)->first();
                            $old_nbre_cotise = $cotisation->nbre_cotise;
                            if ($old_nbre_cotise >= 372) {
                                $response = [
                                    'success' => false,
                                    'message' => "Le nombre de cotisation {$old_nbre_cotise} pour ce carnet est atteint",
                                    'result' => [
                                        'cotisation' => $cotisation,
                                        'mode' => $request->mode,
                                    ],
                                ];
                                return $this->apiResponse($response, $status);
                            } else {
                                $monthPayment = $this->getMonthPayments($request->subscription_id, $request->month);
                                //update cotisation
                                $total_amount = $cotisation->total_amount + $request->amount;
                                $up_nbre_cotise = $old_nbre_cotise + $nbre_cotise;
                                $real_total_amount = $tarif * 372;
                                $now = Carbon::now()->format('Y-m-d H:i:s');
                                $end_at = ($total_amount >= $real_total_amount) ? Carbon::now()->format('Y-m-d H:i:s') : null;

                                $cotisation->update([
                                    'date_cotise' => $now,
                                    'description' => "Cotisation du {$now}",
                                    'nbre_cotise' => $up_nbre_cotise,
                                    'total_amount' => $total_amount,
                                    'collector_id' => $collector->id,
                                    'subscription_id' => $request->subscription_id,
                                    'end_at' => $end_at,
                                    'status' => 'in_progress',
                                ]);
                                if ($end_at !== null) {
                                    $subscription->status = "finished";
                                    $subscription->save();
                                    $cotisation->update([
                                        'status' => "finished",
                                    ]);
                                } else {
                                    $subscription->status = "pending";
                                    $subscription->save();
                                }
                                //add payment for amount paid
                                $metadata = $monthPayment != null ? $monthPayment->metadata : [];
                                // $payments = (array) $request->payments;
                                $newMonthAmountForUpdate = 0;
                                if ($monthPayment != null) {
                                    // Log les metadata avant traitement
                                    Log::info('Metadata before processing');
                                    $new_payments = $request->payments;
                                    Log::info('Payments'.json_encode($new_payments));
                                    $new_payments = array_map(function ($item, $index) {
                                        return [
                                            'id' => Str::uuid(),
                                            'key' => $item['key'],
                                            'month' => $item['month'],
                                            'amount' => $item['amount'],
                                            'payment_date' => $item['payment_date'] ?? Carbon::now()->format('Y-m-d'),
                                            'status' => $item['status'] ?? 'completed',
                                        ];
                                    }, $new_payments, array_keys($new_payments));

                                    Log::info('Metadata after processing'.json_encode($new_payments));
                                    // Appel de la fonction updateMetadata
                                    $updateMetadataResult = $this->updateMetadata($metadata, $new_payments);
                                    Log::info('updateMetadataResult'.json_encode($updateMetadataResult));
                                    if ($updateMetadataResult['success'] == false) {
                                        $response = [
                                            'success' => false,
                                            'message' => "Echec de la mise à jour des métadonnées",
                                            'result' => null,
                                            'errors' => [
                                                'message' => $updateMetadataResult['message'],
                                            ],
                                        ];
                                    } else {

                                        // Mise à jour des variables
                                        $metadata = $updateMetadataResult['result']['metadata'];
                                        $newMonthAmountForUpdate = $updateMetadataResult['result']['totalAmountDiffPayments'] + $monthPayment->amount;

                                        // Log les metadata après traitement
                                    }

                                } else {
                                    $metadata = $request->payments;
                                    $metadata = array_map(function ($item, int $index) {
                                        return [
                                            'id' => Str::uuid(),
                                            'key' => $item['key'],
                                            'month' => $item['month'],
                                            'amount' => $item['amount'],
                                            'payment_date' => $item['payment_date'] ?? Carbon::now()->format('Y-m-d'),
                                            'status' => $item['status'] ?? 'completed',
                                        ];
                                    }, $metadata, array_keys($metadata));
                                }
                                $newMonthAmountForCreate = $monthPayment != null ? $monthPayment->amount + $request->amount : $request->amount;
                                $newMonthPayment = [];
                                if ($monthPayment != null) {
                                    $monthPayment->update([
                                        'metadata' => $metadata,
                                        'description' => "Cotisation du {$now}",
                                        'amount' => $newMonthAmountForUpdate > 0 ? $newMonthAmountForUpdate : $monthPayment->amount,
                                        'payment_date' => Carbon::now()->format('Y-m-d H:i:s'),
                                        'status' => count($metadata) == 31 ? 'completed' : 'pending',
                                    ]);
                                    $monthPayment = $monthPayment->fresh();
                                } else {
                                    $newMonthPayment = $cotisation->payments()->create([
                                        'amount' => $newMonthAmountForCreate,
                                        'description' => "Première cotisation du {$now}",
                                        'pack_id' => $subscription->pack_id,
                                        'collector_id' => $collector->id,
                                        'subscription_id' => $request->subscription_id,
                                        'payment_date' => Carbon::now()->format('Y-m-d'),
                                        'payment_mode' => 'CASH',
                                        'agency_id' => $agency->id,
                                        'client_id' => $client->id,
                                        'status' => $request->nbre_cotise == 31 ? "completed" : "pending",
                                        'payment_type' => 'COLLECT',
                                        'month' => $request->month,
                                        'metadata' => $metadata,
                                    ]);
                                }

                                //credit collector wallet
                                $wallet = $collector->user->wallet;
                                $credite = $this->credite_wallet($wallet, $request->amount);

                                if ($credite['success'] == true) {
                                    DB::commit();
                                    $response = [
                                        'success' => true,
                                        'message' => "Cotisation ajoutée avec succès",
                                        'result' => [
                                            'cotisation' => $cotisation,
                                            'monthPayments' => $monthPayment != null ? $monthPayment : $newMonthPayment,
                                            'payments' => $cotisation->payments()->get(),
                                            'credite' => $credite,
                                            'metadata' => $metadata,
                                            'mode' => $request->mode,
                                        ],
                                    ];

                                    // send push notification
                                    $title = "Cotisation";
                                    $cotisation_text = $nbre_cotise > 1 ? "Cotisations" : "Cotisation";
                                    $body = "Vous venez d'effectuer {$nbre_cotise} {$cotisation_text} de {$request->amount} FCFA dans le carnet {$subscription->code} du client {$client->nom} {$client->prenoms}";
                                    if ($collector->user->device_token !== null) {
                                        $this->firebaseService->sendNotification($collector->user->device_token, $title, $body);
                                    }

                                    // send push notification to  admins and others active and connected collectors
                                    $active_connected_admins_collectors_users_device_tokens = $this->get_active_connected_users_device_tokens_by_roles([1, 2, 3], [$collector->user->id]);
                                    if ($active_connected_admins_collectors_users_device_tokens['success'] == true) {
                                        $tokens = $active_connected_admins_collectors_users_device_tokens['result'];
                                        if (count($tokens) > 0) {
                                            $body = "Le collecteur {$collector->user->username} viens d'effectuer {$nbre_cotise} {$cotisation_text} de {$request->amount} FCFA dans le carnet {$subscription->pack->name} du client {$client->nom} {$client->prenoms}";
                                            $this->firebaseService->sendNotifications($tokens, $title, $body);
                                        }
                                    }
                                } else {
                                    DB::rollback();
                                    $response = [
                                        'success' => false,
                                        'message' => "Echec d'ajout de la cotisation",
                                        'result' => null,
                                        'except' => $credite,
                                    ];
                                }
                            }
                        }
                    }
                }
            }
        } catch (\Throwable $th) {
            Log::error('erreur addcCtisationWithKeysV2: ' . $th->getMessage());
            $response = [
                'success' => false,
                'message' => "Echec de l'ajout de la cotisation",
                'result' => null,
                'errors' => $th->getMessage(),
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * add_cotisation function
     * Ajout d'une cotisation
     * @param Request $request
     * @return JsonResponse $response
     */
    public function add_client_cotisation(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'client_id' => 'required|exists:clients,id',
                'amount' => 'required|numeric',
                'subscription_id' => 'required|exists:subscriptions,id',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag(),
                ];
            } else {
                $amount = $request->amount;
                $collector = $this->get_personal_auth($request);
                $agency = $collector->currentAgency()->first();
                $client = $this->get_client_byId($request->client_id);
                $subscription = $client->subscriptions()->where('id', $request->subscription_id)->first();
                if ($subscription == null) {
                    $response = [
                        'success' => false,
                        'message' => "Ce client n'est pas abonné à ce pack",
                        'result' => [
                            'client' => $client,
                            'subscription' => $subscription,
                        ],
                        'errors' => null,
                    ];
                } else {
                    // check if subscription status in finised
                    if ($subscription->status == "finished") {
                        $response = [
                            'success' => false,
                            'message' => "La cotisation pour ce carnet produit est dejà arrivée à son terme.",
                            'result' => [
                                'subscription' => $subscription,
                                'mode' => $request->mode,
                            ],
                        ];
                    } else {

                        $check_first = $this->check_first_cotisation_by_client($client->id, $request->subscription_id);
                        $tarif = $client->subscriptions()->where('id', $request->subscription_id)->first()->pack->tarif;
                        // $nbre_cotise = $request->nbre_cotise ?? ceil($request->amount/$tarif);
                        $nbre_cotise = (isset($request->nbre_cotise)) ? $request->nbre_cotise : ceil($request->amount / $tarif);
                        // dd($check_first);
                        if ($check_first == null) {
                            $now = Carbon::now()->format('Y-m-d');
                            $cotisation = $client->cotisations()->create([
                                'date_cotise' => $now,
                                'nbre_cotise' => $nbre_cotise,
                                'total_amount' => $request->amount,
                                'collector_id' => $collector->id,
                                'subscription_id' => $request->subscription_id,
                                'description' => "Première cotisation {$now}",
                                'agency_id' => $agency->id,
                                'start_at' => $now,
                            ]);
                            $subscription->status = $nbre_cotise == 372 ? "finished" : "started";
                            $subscription->started_at = Carbon::now()->format('Y-m-d');
                            $subscription->finished_at = Carbon::now()->addDays($subscription->pack->duration)->addDay(-1);
                            $subscription->save();
                            //add payment for amount paid
                            $payments = [];
                            $description = "";
                            for ($i = 0; $i < $nbre_cotise; $i++) {
                                # add payment
                                $cotisation_status = $cotisation->nbre_cotise == 372 ? "finished" : "started";
                                $payment_date = Carbon::now()->addDays($i)->format('Y-m-d');
                                $index = $i + 1;
                                $description = "Cotisation du {$payment_date}";
                                $payment[] = $cotisation->payments()->create([
                                    'amount' => $tarif,
                                    'description' => $description,
                                    'collector_id' => $collector->id,
                                    'subscription_id' => $request->subscription_id,
                                    'payment_date' => $payment_date,
                                    'payment_mode' => 'CASH',
                                    'agency_id' => $agency->id,
                                    'client_id' => $client->id,
                                    'status' => "completed",
                                ]);
                                $cotisation->update([
                                    'description' => $description,
                                    'status' => $cotisation_status,
                                ]);
                            }

                            $wallet = $collector->user->wallet;
                            $credite = $this->credite_wallet($wallet, $request->amount);

                            $response = [
                                'success' => true,
                                'message' => "Cotisation ajoutée avec succès",
                                'result' => [
                                    'cotisation' => $cotisation,
                                    'payments' => $payment,
                                    'credite' => $credite,
                                ],
                            ];
                        } else {
                            $cotisation = $client->cotisations()->where('subscription_id', $request->subscription_id)->first();
                            $old_nbre_cotise = $cotisation->nbre_cotise;
                            if ($old_nbre_cotise > 372) {
                                $response = [
                                    'success' => false,
                                    'message' => "Le nombre de cotisation {$old_nbre_cotise} pour ce carnet est atteins",
                                    'result' => [
                                        'cotisation' => $cotisation,
                                        'mode' => $request->mode,
                                    ],
                                ];
                            } else {
                                $total_amount = $cotisation->total_amount + $request->amount;
                                $up_nbre_cotise = $old_nbre_cotise + $nbre_cotise;
                                $real_total_amount = $tarif * 372;
                                $now = Carbon::now()->format('Y-m-d');
                                $end_at = ($total_amount >= $real_total_amount) ? $now : null;
                                $cotisation->update([
                                    'date_cotise' => $now,
                                    'description' => "Cotisation du {$now}",
                                    'nbre_cotise' => $up_nbre_cotise,
                                    'total_amount' => $total_amount,
                                    'collector_id' => $collector->id,
                                    'subscription_id' => $request->subscription_id,
                                    'end_at' => $end_at,
                                    'status' => 'in_progress',
                                ]);
                                if ($end_at !== null) {
                                    $subscription->status = "finished";
                                    $subscription->save();
                                    $cotisation->update([
                                        'status' => "finished",
                                    ]);
                                } else {
                                    $subscription->status = "pending";
                                    $subscription->save();
                                }
                                //add payment for amount paid
                                $payments = [];
                                $start_at = $cotisation->start_at;
                                $last_payment_date = Payment::where('subscription_id', $request->subscription_id)->where('cotisation_id', $cotisation->id)->orderBy('payment_date', 'desc')->first()->payment_date;
                                $last_payment_date = Carbon::parse($last_payment_date)->addDay()->format('Y-m-d');
                                $date_cotise = $request->date_cotise;
                                $old_payments_date = $cotisation->payments()->pluck('payment_date')->toArray();
                                $date_diff = Carbon::parse($date_cotise)->diffInDays(Carbon::parse($last_payment_date));
                                $arrieres = [];
                                for ($i = 0; $i < $date_diff; $i++) {
                                    if (!in_array(Carbon::parse($last_payment_date)->addDays($i)->format('Y-m-d'), $old_payments_date)) {
                                        $arrieres[] = Carbon::parse($last_payment_date)->addDays($i)->format('Y-m-d');
                                    }
                                }
                                $amount_remaining = $amount;
                                $description = "";
                                foreach ($arrieres as $key => $arr) {
                                    $cotisation_status = $cotisation->nbre_cotise == 372 ? "finished" : "started";
                                    $description = "Cotisation du {$arr}";
                                    if ($amount_remaining >= $tarif) {
                                        $amount_remaining -= $tarif;
                                        $payment[] = $client->payments()->create([
                                            'amount' => $tarif,
                                            'description' => $description,
                                            'collector_id' => $collector->id,
                                            'subscription_id' => $request->subscription_id,
                                            'payment_date' => $arr,
                                            'payment_mode' => 'CASH',
                                            'agency_id' => $agency->id,
                                            'client_id' => $client->id,
                                            'cotisation_id' => $cotisation->id,
                                            'status' => "completed",
                                        ]);
                                        $cotisation->update([
                                            'description' => $description,
                                            'status' => $cotisation_status,
                                        ]);
                                    }
                                }

                                if ($amount_remaining >= $tarif) {
                                    $cotisation_status = $cotisation->nbre_cotise == 372 ? "finished" : "started";
                                    if ($nbre_cotise == 1) {
                                        # cotisation unique
                                        $next_day = Carbon::parse($last_payment_date)->format('Y-m-d');
                                        $description = "Cotisation du {$next_day}";
                                        $payments[] = $client->payments()->create([
                                            'amount' => $tarif,
                                            'description' => $description,
                                            'collector_id' => $collector->id,
                                            'subscription_id' => $request->subscription_id,
                                            'payment_date' => $next_day,
                                            'payment_mode' => 'CASH',
                                            'agency_id' => $agency->id,
                                            'client_id' => $client->id,
                                            'cotisation_id' => $cotisation->id,
                                        ]);
                                        $cotisation->update([
                                            'description' => $description,
                                            'status' => $cotisation_status,
                                        ]);
                                    } else {
                                        # cotisation multiple
                                        for ($i = 0; $i < $nbre_cotise; $i++) {
                                            # add payment
                                            $cotisation_status = $cotisation->nbre_cotise == 372 ? "finished" : "started";
                                            if ($amount_remaining >= $tarif) {
                                                $amount_remaining -= $tarif;
                                                $next_day = Carbon::parse($last_payment_date)->addDays($i)->format('Y-m-d');
                                                $description = "Cotisation du {$next_day}";
                                                $payments[] = $client->payments()->create([
                                                    'amount' => $tarif,
                                                    'description' => $description,
                                                    'collector_id' => $collector->id,
                                                    'subscription_id' => $request->subscription_id,
                                                    'payment_date' => $next_day,
                                                    'payment_mode' => 'CASH',
                                                    'agency_id' => $agency->id,
                                                    'client_id' => $client->id,
                                                    'cotisation_id' => $cotisation->id,
                                                ]);
                                                $cotisation->update([
                                                    'description' => $description,
                                                    'status' => $cotisation_status,
                                                ]);
                                            }
                                        }
                                    }
                                }

                                $wallet = $collector->user->wallet;
                                $credite = $this->credite_wallet($wallet, $request->amount);
                                $response = [
                                    'success' => true,
                                    'message' => "Cotisation ajoutée avec succès",
                                    'result' => [
                                        'cotisation' => $cotisation,
                                        'payments' => $payments,
                                        'credite' => $credite,
                                        'mode' => $request->mode,
                                    ],
                                ];
                            }
                        }
                    }
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de l'ajout de la cotisation",
                'result' => null,
                'errors' => $th->getMessage(),
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * get_client_cotisations function
     * Récupération des cotisations d'un client
     * @param Request $request
     * @return JsonResponse
     */
    public function get_client_cotisations(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'client_id' => 'required|exists:clients,id',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag(),
                ];
            } else {
                $client = $this->get_client_byId($request->client_id);
                $cotisations = $client->cotisations()->with(['payments'])->get();
                $response = [
                    'success' => true,
                    'message' => "Cotisations du client",
                    'result' => $cotisations,
                ];
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des cotisations du client",
                'result' => null,
                'errors' => $th->getMessage(),
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * get_all_versements function
     * Récupération des versements d'un collecteur
     * @param Request $request
     * @return void
     */
    public function get_all_versements(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $filter = $request->filter;
            // dd($filter);
            $type = $filter['type'] ? $filter['type'] : 'month';
            $value = $filter['value'] ? $filter['value'] : Carbon::now()->format('m');
            $collector = $this->get_personal_auth($request);
            // $agency = $collector->currentAgency()->first();
            // $versements = $collector->versements()->with(['cashier','agency'])->get();
            // dd($versements);
            switch ($type) {
                case 'day':
                    // $versements = $collector->versements()->where('agency_id', $agency->id)->whereDate('payment_date', $value)->orderBy('created_at', 'desc')->get();
                    $versements = $collector->versements()->whereDate('payment_date', $value)->orderBy('created_at', 'desc')->get();
                    break;
                case 'week':
                    # get versements by week
                    $current_week = Carbon::now()->startOfWeek();
                    // dd($current_week);
                    // $versements = $collector->versements()->where('agency_id', $agency->id)->whereBetween('payment_date', [
                    //     Carbon::now()->startOfWeek(),
                    //     Carbon::now()->endOfWeek(),
                    // ])->orderBy('created_at', 'desc')->get();
                    $versements = $collector->versements()->whereBetween('payment_date', [
                        Carbon::now()->startOfWeek(),
                        Carbon::now()->endOfWeek(),
                    ])->orderBy('created_at', 'desc')->get();
                    break;
                case 'month':
                    # get versements by month
                    // dd(Carbon::now()->startOfMonth());
                    $current_month = Carbon::now()->format('m');
                    // $versements = $collector->versements()->where('agency_id', $agency->id)->whereMonth('payment_date', $current_month)->orderBy('created_at', 'desc')->get();
                    $versements = $collector->versements()->whereMonth('payment_date', $current_month)->orderBy('created_at', 'desc')->get();
                    // dd($versements);
                    break;
                case 'year':
                    # get versements by year
                    $current_year = Carbon::now()->format('Y');
                    // dd($current_year);
                    // $versements = $collector->versements()->where('agency_id', $agency->id)->whereYear('payment_date', $current_year)->orderBy('created_at', 'desc')->get();
                    $versements = $collector->versements()->whereYear('payment_date', $current_year)->orderBy('created_at', 'desc')->get();
                    break;

                default:
                    # retourner pour le mois courant
                    // dd($filter);
                    $current_month = Carbon::now()->format('m');
                    // $versements = $collector->versements()->where('agency_id', $agency->id)->whereMonth('payment_date', $current_month)->orderBy('created_at', 'desc')->get();
                    $versements = $collector->versements()->whereMonth('payment_date', $current_month)->orderBy('created_at', 'desc')->get();
                    break;
            }
            // dd($versements);
            $response = [
                'success' => true,
                'message' => "Liste des versements",
                'result' => [
                    'versements' => $versements,
                    'wallet' => $collector->user->wallet,
                ],
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des versements",
                'result' => null,
                'errors' => $th->getMessage(),
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * getCurrentCollectorActivities function
     * Récupération des activités du jour du collecteur
     *
     * @param Request $request
     * @return void
     */
    public function getCurrentCollectorActivities(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $collector = $this->get_personal_auth($request);

            $now = Carbon::now()->format('Y-m-d');
            $vente_carnets = $collector->payments()->whereDate('created_at', $now)->whereNotNull('subscription_id')->where('payment_type', 'CARNET')->whereNull('versement_id'); //vente de carnet

            $total_vente_carnets = $vente_carnets->sum('amount');
            $total_vente_carnets_count = $vente_carnets->count();


            // dd($subscribes);
            $total_collected = 0;
            $total_collected_count = 0;

            $collects = $collector->payments()->whereDate('updated_at', $now)->whereNotNull('cotisation_id')->where('payment_type', 'COLLECT')->whereNull('versement_id')->get(); //cotisation
            foreach ($collects as $collect) {
                $metadata = $collect->metadata;
                $today_metadata = array_filter($metadata, function ($meta) use ($now) {
                    return Carbon::parse($meta['payment_date'])->format('Y-m-d') == $now;
                });
                $total_collected += array_sum(array_column($today_metadata, 'amount'));
                $total_collected_count += count($today_metadata);
            }

            $current_total_amount = $total_vente_carnets + $total_collected; // total amount of current day activities
            //check old versements in day
            $old_versements_in_day = $collector->versements()->where('status', 'confirmed')->whereDate('payment_date', $now)->sum('amount');

            $old_activities_amount = 0; //
            $collector_balance = $collector->user->wallet->balance;
            if ($collector_balance >= 0 && $collector_balance >= $current_total_amount) {
                $old_activities_amount = (float) $collector_balance - $current_total_amount;
            }
            $total_amountPaid = $current_total_amount + $old_activities_amount;

            $today_activites = [
                'total_amount_vente_carnets' => $total_vente_carnets,
                'total_vente_carnets_count' => $total_vente_carnets_count,
                'total_amount_collected' => $total_collected,
                'total_collected_count' => $total_collected_count,
                'current_total_amount' => $current_total_amount,
                'old_amount_versements_in_day' => $old_versements_in_day,
                'old_activities_amount' => $old_activities_amount,
                'balance' => $collector_balance,
                'total_amount_paid' => $total_amountPaid,
            ];

            $response = [
                'success' => true,
                'message' => "Activités du jour",
                'result' => $today_activites,
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des activités du jour",
                'result' => null,
                'errors' => $th->getMessage(),
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }


    /**
     * getCurrentCollectorActivitiesV2 function
     * Récupération des activités du jour du collecteur
     *
     * @param Request $request
     * @return void
     */
    public function getCurrentCollectorActivitiesV2(Request $request)
    {
        $response = [];
        $status = 200;

        try {
            $collector = $this->get_personal_auth($request);
            $now = Carbon::now()->format('Y-m-d');

            // Calcul des ventes de carnets et du nombre de carnets vendus dans une seule requête
            $vente_carnets = $collector->payments()
                ->whereDate('created_at', $now)
                ->whereNotNull('subscription_id')
                ->where('payment_type', 'CARNET')
                ->whereNull('versement_id');

            $total_vente_carnets = $vente_carnets->sum('amount');
            $total_vente_carnets_count = $vente_carnets->count();

            // Calcul des collectes de cotisations et du nombre de collectes
            $collects = $collector->payments()
                ->whereDate('updated_at', $now)
                ->whereNotNull('cotisation_id')
                ->where('payment_type', 'COLLECT')
                ->whereNull('versement_id');

            $total_collected = 0;
            $total_collected_count = 0;

            foreach ($collects as $collect) {
                $metadata = $collect->metadata;
                $today_metadata = array_filter($metadata, function ($meta) use ($now) {
                    return Carbon::parse($meta['payment_date'])->format('Y-m-d') == $now;
                });
                $total_collected += array_sum(array_column($today_metadata, 'amount'));
                $total_collected_count += count($today_metadata);
            }

            // Total des activités du jour
            $current_total_amount = $total_vente_carnets + $total_collected;

            // Somme des anciens versements confirmés du jour
            $old_versements_in_day = $collector->versements()
                ->where('status', 'confirmed')
                ->whereDate('payment_date', $now)
                ->sum('amount');

            // Récupération du solde du wallet
            $collector_balance = $collector->user->wallet->balance;

            // Calcul des anciennes activités du jour
            $old_activities_amount = max(0, $collector_balance - $current_total_amount);

            // Montant total payé aujourd'hui
            $total_amountPaid = $current_total_amount + $old_activities_amount;

            // Construction de la réponse finale
            $today_activites = [
                'total_amount_vente_carnets' => $total_vente_carnets,
                'total_vente_carnets_count' => $total_vente_carnets_count,
                'total_amount_collected' => $total_collected,
                'total_collected_count' => $total_collected_count,
                'current_total_amount' => $current_total_amount,
                'old_amount_versements_in_day' => $old_versements_in_day,
                'old_activities_amount' => $old_activities_amount,
                'balance' => $collector_balance,
                'total_amount_paid' => $total_amountPaid,
            ];

            $response = [
                'success' => true,
                'message' => "Activités du jour",
                'result' => $today_activites,
            ];
        } catch (\Throwable $th) {
            // Gestion des erreurs
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des activités du jour",
                'result' => null,
                'errors' => $th->getMessage(),
            ];
            $status = 500;
        }

        return $this->apiResponse($response, $status);
    }




    /**
     * init_versement function
     *  Initialisation d'un versement
     * @param Request $request
     * @return JsonResponse $response
     */
    public function init_versement(Request $request)
    {
        $response = [];
        $status = 200;
        DB::beginTransaction();
        try {
            $validate = Validator::make($request->all(), [
                'amount' => 'required|numeric',
                'secret_code' => 'required',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag(),
                ];
            } else {
                $collector = $this->get_personal_auth($request);
                // dd($collector);
                $user = $collector->user;
                if (Crypt::decryptString($user->secret_code) != $request->secret_code) {
                    $response = [
                        'success' => false,
                        'message' => "Code secret incorrect",
                        'result' => null,
                        'errors' => null,
                    ];
                } else {

                    $collector_wallet = $collector->user->wallet;
                    // dd($collector_wallet);
                    $agency = $collector->currentAgency()->first();
                    // dd($agency);
                    $today = Carbon::now()->format('Y-m-d H:i:s');
                    $check_versement = $collector->versements()->where('status', '=', 'confirmed')->whereDate('payment_date', $today)->count(); // only versements confirmed(which have been processed by a cashier agent with success)
                    // dd($check_versement);
                    $collector_activities = $this->get_collector_activities_by_day($collector);
                    // dd($collector_activities);
                    $res_status = "initialized";
                    $amount_remaining = 0;
                    $amount = $request->amount;
                    $message = "";
                    // dd($collector_activities);
                    if ($check_versement >= 10 && $collector_wallet->balance <= 0) {
                        $response = [
                            'success' => false,
                            'message' => "Désolé, vous avez atteint la limite de versement pour cette journée",
                            'result' => $check_versement,
                        ];
                    } else {
                        # code...
                        // $wallet = $this->get_auth_user($request)->wallet()->first();
                        if ($amount <= $collector_wallet->balance) {
                            # code...
                            $amount_remaining = $collector_wallet->balance - $amount; // day remaining

                            // dd($amount_remaining);
                            if ($amount_remaining == 0 && $collector_wallet->balance == $amount) {
                                $res_status = "completed"; // Signifie que le collecteur a versé tous ses collectes(vente de carnets et cotisations) du jour
                                $amount_remaining = 0;
                                $message = "Versement initialisé avec succès";
                            } else {
                                $message = "Versement initialisé avec succès. Il vous reste " . $amount_remaining . " FCFA à verser";
                                $res_status = "in_progress"; // Signifie que le collecteur n'a tous versés. Il pourra toujours initier un autre versement pour compléter les versements du jour
                            }
                            $qrcode = implode('', array_map(function ($item) {
                                return rand(0, 9);
                            }, range(0, 7)));
                            $expired_at = Carbon::now()->addDays(3)->format('Y-m-d H:i:s');
                            $versement = $collector->versements()->create([
                                'amount' => (float) $request->amount,
                                'agency_id' => $agency->id,
                                'payment_date' => $today,
                                'qrcode' => $qrcode,
                                'expired_at' => $expired_at,
                                'status' => $res_status,
                                'token' => Str::uuid(),
                                'amount_remaining' => (float) $amount_remaining,
                            ]);
                            if ($versement !== null) {
                                DB::commit();
                                $response = [
                                    'success' => true,
                                    'message' => $message,
                                    'result' => [
                                        'versement' => $versement,
                                        'collector_day_activities' => $collector_activities,
                                        'qrcode' => $qrcode,
                                        'expired_at' => $expired_at,
                                    ],
                                ];

                                // send push notification
                                $title = "Versement";
                                $body = "Vous venez d'initier un versement ({$qrcode}) de {$request->amount} FCFA";
                                if ($user->device_token !== null) {
                                    $this->firebaseService->sendNotification($user->device_token, $title, $body);
                                }

                                // send push notification to active and connected cashiers
                                $active_connected_admins_cashiers_users_device_tokens = $this->get_active_connected_users_device_tokens_by_roles([1, 2, 3, 5, 10, 12]);
                                if ($active_connected_admins_cashiers_users_device_tokens['success'] == true) {
                                    $tokens = $active_connected_admins_cashiers_users_device_tokens['result'];
                                    if (count($tokens) > 0) {
                                        $body = "Le collecteur {$user->username} viens d'initier un versement ({$qrcode}) de {$request->amount} FCFA";
                                        $this->firebaseService->sendNotifications($tokens, $title, $body);
                                    }
                                }
                            } else {
                                DB::rollback();
                                $response = [
                                    'success' => false,
                                    'message' => "Echec de l'initialisation du versement",
                                    'result' => null,
                                    'errors' => null,
                                ];
                            }
                        } else {
                            DB::rollback();
                            $message = $collector_wallet->balance == 0 ? "Désolé,vous avez déjà effectué un versement pour cette journée"
                            : "Vous ne pouvez pas verser un montant supérieur votre montant restant à verser {$collector_wallet->balance}";
                            $response = [
                                'success' => false,
                                'message' => $message,
                                'result' => [
                                    'versement_count' => $check_versement,
                                    'collector_day_activities' => $collector_activities,
                                    'collector_balance' => $collector_wallet->balance,
                                ],
                                'errors' => null,
                            ];
                        }
                    }
                }
            }
        } catch (\Throwable $th) {
            DB::rollback();
            $response = [
                'success' => false,
                'message' => "Echec de l'initialisation du versement",
                'result' => null,
                'except' => $th->getMessage(),
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * reset_versement_expiration function
     * Réinitialisation de l'expiration d'un versement
     * @param Request $request
     * @return JsonResponse $response
     */
    public function reset_versement_expiration(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'secret_code' => 'required',
                'versement_id' => 'required|exists:versements,id',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag(),
                ];
            } else {
                $collector = $this->get_personal_auth($request);
                $user = $collector->user;
                if (Crypt::decrypt($user->secret_code) != $request->secret_code) {
                    $response = [
                        'success' => false,
                        'message' => "Code secret incorrect",
                        'result' => null,
                        'errors' => null,
                    ];
                } else {
                    $versement = $collector->versements()->where('id', $request->versement_id)->first();
                    if ($versement !== null) {
                        $versement->expired_at = Carbon::now()->addMinutes(10)->format('Y-m-d H:i:s');
                        $versement->save();
                        $response = [
                            'success' => true,
                            'message' => "Versement réinitialisé avec succès",
                            'result' => [
                                'versement' => $versement,
                                'qrcode' => $versement->qrcode,
                                'expired_at' => $versement->expired_at,
                            ],
                        ];
                    } else {
                        $response = [
                            'success' => false,
                            'message' => "Echec de la réinitialisation du versement",
                            'result' => null,
                            'errors' => null,
                        ];
                    }
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la réinitialisation du versement",
                'result' => null,
                'errors' => $th->getMessage(),
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    private function updateMetadata($metadata, $newPayments)
    {
        $response = [];
        try {
            // Extraire toutes les valeurs de 'key' dans $metadata
            $metadataKeys = array_column($metadata, 'key');

            // Filtrer les éléments de $newPayments dont la 'key' ne figure pas dans $metadataKeys
            $filteredNewPayments = array_filter($newPayments, function ($payment) use ($metadataKeys) {
                return !in_array($payment['key'], $metadataKeys);
            });

            // Afficher les résultats filtrés
            // Réindexer pour obtenir un tableau d'objets au lieu de tableau associatif
            $filteredNewPayments = array_values($filteredNewPayments);
            $metadata = array_merge($metadata, $filteredNewPayments);
            // Calculer le montant total des nouveaux paiements filtrés
            $totalAmountDiffPayments = array_sum(array_column($filteredNewPayments, 'amount'));
            $result = [
                'metadata' => $metadata,
                'diffPayments' => $filteredNewPayments,
                'totalAmountDiffPayments' => $totalAmountDiffPayments,
            ];
            $response = [
                'success' => true,
                'message' => "Liste des metadata",
                'result' => $result,
            ];
        } catch (\Throwable $th) {
            Log::error('erreur updateMetadata', $th->getMessage());
            $response = [
                'success' => false,
                'message' => $th->getMessage(),
                'result',
            ];
        }
        return $response;
    }
}
