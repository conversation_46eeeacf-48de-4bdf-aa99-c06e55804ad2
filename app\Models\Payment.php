<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Payment extends Model
{
    use HasFactory;
    protected $fillable = [
        'cotisation_id','payment_date', 'month', 'key','amount','description','status','collector_id','subscription_id',
        'agency_id','pack_id','payment_mode','payment_date','client_id','payment_type','metadata', 'created_at', 'updated_at', 'versement_id' # remove later after refactoring payments
    ];

    protected $casts = [
        'metadata' => 'array'
    ];

    public function cotisation()
    {
        return $this->belongsTo(Cotisation::class, 'cotisation_id');
    }

    public function collector()
    {
        return $this->belongsTo(Personal::class, 'collector_id');
    }

    public function subscription()
    {
        return $this->belongsTo(Subscription::class, 'subscription_id');
    }

    public function agency()
    {
        return $this->belongsTo(Agency::class, 'agency_id');
    }

    public function pack()
    {
        return $this->belongsTo(Pack::class, 'pack_id');
    }

    public function client(){
        return $this->belongsTo(Client::class, 'client_id');
    }

    public function versement(){
        return $this->belongsTo(Versement::class, 'versement_id');
    }


}
