<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Personal extends Model
{
    use HasFactory;
    protected $fillable = [
        'user_id','quarter_id','city_id','nom','prenoms',
        'email','phone','status','permissions','photo','is_affected','role_id'
    ];

    protected $casts = [
        'permissions' => 'array',
    ];

    /**
     * user function
     * Get the user
     * @return Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function role()
    {
        return $this->belongsTo(Role::class,'role_id');
    }

    public function quarter()
    {
        return $this->belongsTo(Quarter::class, 'quarter_id');
    }

    public function city()
    {
        return $this->belongsTo(City::class, 'city_id');
    }

    /**
     * The agencies that belong to the Personal
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function agencies(): BelongsToMany
    {
        return $this->belongsToMany(Agency::class, 'agency_personals', 'personal_id', 'agency_id')
        ->withPivot(['personal_id','agency_id','is_current','role_id','affected_at','mutated_at'])->withTimestamps();
    }

    /**
     * The currentAgency that belong to the Personal
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function currentAgency(): BelongsToMany
    {
        return $this->belongsToMany(Agency::class, 'agency_personals', 'personal_id', 'agency_id')
        ->wherePivot('is_current',1);
    }

    /**
     * Get all of the versements for the AgencyPersonal
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function versements()
    {
        return $this->hasMany(Versement::class, 'collector_id', 'id');
    }

    /**
     * Get all of the cashier_versements for the AgencyPersonal
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function cashier_versements(): HasMany
    {
        return $this->hasMany(Versement::class, 'cashier_id', 'id');
    }

    public function cotisations()
    {
        return $this->hasMany(Cotisation::class, 'collector_id', 'id');
    }

    public function payments()
    {
        return $this->hasMany(Payment::class, 'collector_id', 'id');
    }

    // /**
    //  * Get all of the clients for the AgencyPersonal
    //  *
    //  * @return \Illuminate\Database\Eloquent\Relations\HasMany
    //  */
    // public function clients(): HasMany
    // {
    //     return $this->hasMany(Client::class, 'collector_id', 'id');
    // }

    /**
     * The clients that belong to the Personal
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function clients(): BelongsToMany
    {
        return $this->belongsToMany(Client::class, 'client_collectors', 'collector_id', 'client_id')
        ->withPivot(['agency_id','is_principal','status','collector_id','client_id'])
        ->withTimestamps();
    }

    /**
     * Get all of the subscriptions for the AgencyPersonal
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class, 'collector_id', 'id');
    }

    /**
     * Get all of the controls for the Personal
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function controls(): HasMany
    {
        return $this->hasMany(Control::class, 'supervisor_id', 'id');
    }

}
