<?php

namespace App\Http\Controllers\API\admin;

use App\Models\Product;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Rules\AddProductRule;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\API\helpers\HelperController;
use App\Models\Category;

class ProductController extends HelperController
{
    /**
     * get_all_products function
     * Get all products
     * @param Request $request
     * @return void
     */
    public function get_all_products(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $page = $request->input('page', 1);
            $perPage = $request->input('limit', 20);
            $products = Product::orderBy('created_at', 'desc')->with(['category'])
                ->withCount(['packs'])
                ->paginate($perPage, ['*'], 'page', $page);;
            $response = [
                'success' => true,
                'message' => "Liste des produits",
                'result' => $products
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des produits",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * get_all_categories function
     * Get all categories
     * @param Request $request
     * @return JsonResponse $response
     */
    public function get_all_categories(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $categories = Category::with(['products'])->orderBy('created_at', 'desc')->get();
            $response = [
                'success' => true,
                'message' => "Liste des catégories",
                'result' => $categories
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des catégories",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * add_products function
     * Add products
     * @param Request $request
     * @return void
     */
    public function add_products(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'products' => [
                    'required',
                    'array',
                    new AddProductRule()
                ],
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
            } else {
                $products = $request->products;
                $errors = [];
                $success = [];
                // dd($products[6]);
                foreach ($products as $prod) {
                    $prod_name = htmlspecialchars(strtoupper($prod['name']));
                    // dd($prod_name);
                    $check_prod_exist = $this->get_product_byName($prod_name);
                    if ($check_prod_exist != null) {
                        $errors[] = $prod;
                    } else {
                        $code_prod = $this->generate_productCode($prod_name);
                        $product = new Product();
                        $product->name = $prod_name;
                        $product->price_achat = $prod['price_achat'];
                        $product->price_vente = $prod['price_vente'];
                        $product->stock_quantity = $prod['stock_quantity'];
                        $product->category_id = $prod['category_id'];
                        $product->code = $code_prod;
                        $image = $product['image'];
                        if ($request->hasFile('image')) {
                            $filename = time() . '_' . $image->getClientOriginalName();
                            $extension = $image->getClientOriginalExtension();
                            $dest_path = public_path('/images/products');
                            $image->move($dest_path, $filename);
                            $product->image = $filename;
                        }
                        $product->save();
                        $success[] = $product;
                        $status = 201;
                    }
                }
                $response = [
                    'success' => true,
                    'message' => "Liste des produits ajoutés",
                    'result' => [
                        'products' => $success,
                        'errors' => $errors
                    ]
                ];
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de l'ajout du produit",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * add_product function
     * Add a single product
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function add_product(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'name' => 'required|string',
                'price_achat' => 'required|numeric',
                'price_vente' => 'nullable|numeric',
                'stock_quantity' => 'nullable|integer',
                'category_id' => 'nullable|integer|exists:categories,id',
                'image' => 'nullable|image|max:2048',  // optional image, max 2MB
                'description' => 'nullable|string',
                'profit_margin' => 'nullable|numeric',
                'profit_margin_type' => 'nullable|string',
            ]);

            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
            } else {
                $prod_name = htmlspecialchars(strtoupper($request->name));
                $check_prod_exist = $this->get_product_byName($prod_name);

                if ($check_prod_exist != null) {
                    $response = [
                        'success' => false,
                        'message' => "Le produit existe déjà",
                        'result' => null
                    ];
                } else {
                    $code_prod = $this->generate_productCode($prod_name);
                    $product = new Product();
                    $product->name = $prod_name;
                    $product->price_achat = $request->price_achat;
                    $product->price_vente = $request->price_vente;
                    $product->stock_quantity = $request->stock_quantity;
                    $product->category_id = $request->category_id;
                    $product->code = $code_prod;
                    $product->description = $request->description;
                    $product->margin_profit = $request->profit_margin;
                    $product->profit_margin_type = $request->profit_margin_type;

                    if ($request->hasFile('image')) {
                        $image = $request->file('image');
                        $filename = time() . '_' . $image->getClientOriginalName();
                        $dest_path = public_path('uploads/products');
                        $image->move($dest_path, $filename);
                        $product->image = $filename;
                    }

                    $product->save();
                    $response = [
                        'success' => true,
                        'message' => "Produit ajouté avec succès",
                        'result' => $product
                    ];
                    $status = 201;
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de l'ajout du produit",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }


    /**
     * update_product function
     * Update product by id
     * @param Request $request
     * @return JsonResponse $response
     */
    public function update_product(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                // 'name'=>'required',
                // 'price_achat'=>'required',
                // 'price_vente'=>'required',
                // 'stock_quantity'=>'required',
                'product_id' => 'required',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
            } else {
                $product_id = $request->product_id;
                $product = Product::where('id', $product_id)->first();
                if ($product == null) {
                    $response = [
                        'success' => false,
                        'message' => "Ce produit n'existe pas",
                        'result' => null,
                        'errors' => null
                    ];
                    $status = 400;
                } else {
                    $product->name = $request->name;
                    $product->price_achat = $request->price_achat;
                    $product->price_vente = $request->price_vente;
                    $product->stock_quantity = $request->stock_quantity;
                    $product->category_id = $request->category_id;
                    $image = $request->file('image');
                    if ($request->hasFile('image')) {
                        $filename = time() . '_' . $image->getClientOriginalName();
                        $extension = $image->getClientOriginalExtension();
                        $dest_path = public_path('/images/products');
                        $image->move($dest_path, $filename);
                        $product->image = $filename;
                    }
                    $product->save();
                    $response = [
                        'success' => true,
                        'message' => "Produit modifié avec succès",
                        'result' => $product
                    ];
                    $status = 201;
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la modification du produit",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * get_product_packs function
     *  Get product packs by product id
     * @param Request $request
     * @return void
     */
    public function get_product_packs(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'product_id' => 'required|exists:products,id',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
            } else {
                $product_id = $request->product_id;
                $product = $this->get_product_by_id($product_id);
                if ($product == null) {
                    $response = [
                        'success' => false,
                        'message' => "Ce produit n'existe pas",
                        'result' => null,
                        'errors' => null
                    ];
                    $status = 400;
                } else {
                    $packs = $product->packs()->with([
                        'clients',

                    ])->get();
                    $response = [
                        'success' => true,
                        'message' => "Liste des packs du produit",
                        'result' => [
                            'product' => $product,
                            'packs' => $packs
                        ]
                    ];
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des packs du produit",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * get_alert_products function
     *  Get products in alert
     * @param Request $request
     * @return void
     */
    public function get_alert_products(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $products = Product::where('stock_quantity', '<=', 5)->orWhere('alert_quantity', '>', 0)->get();
            $response = [
                'success' => true,
                'message' => "Liste des produits en alerte",
                'result' => $products
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des produits en alerte",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des produits en alerte",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }


    /**
     * get_clients_subscribe_to_product function
     * Récupère la liste des clients abonnés à un produit
     * @param Request $request
     * @return void
     */
    public function get_clients_subscribe_to_product(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'product_id' => 'required|exists:products,id',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
            } else {
                $product_id = $request->product_id;
                $product = $this->get_product_by_id($product_id);
                $packs = $product->packs;
                $clients = [];
                foreach ($packs as $pack) {
                    $clients[] = $pack->clients;
                }
                $response = [
                    'success' => true,
                    'message' => "Liste des clients abonnés au produit",
                    'result' => [
                        'product' => $product,
                        'clients' => $clients
                    ]
                ];
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des clients abonnés au produit",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * get_total_cotisation_by_product function
     * Récupère le total des cotisations d'un produit
     * @param Request $request
     * @return void
     */
    public function get_total_cotisation_by_product(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'product_id' => 'required|exists:products,id',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
            } else {
                $product_id = $request->product_id;
                $product = $this->get_product_by_id($product_id);
                $packs = $product->packs;
                $total = 0;
                foreach ($packs as $pack) {
                    $total += $pack->cotisations()->sum('amount');
                }
                $response = [
                    'success' => true,
                    'message' => "Total des cotisations du produit",
                    'result' => [
                        'product' => $product,
                        'total' => $total
                    ]
                ];
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec du calcul du total des cotisations du produit",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function addCategories(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'categories' => 'required|array',
                'categories.*name' => 'required|string|unique:categories,name,except,id',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
            } else {
                $categories = $request->categories;
                $result = [];
                foreach ($categories as $cat) {
                    $category_name = htmlspecialchars(strtoupper($cat['name']));
                    $category = new Category();
                    $category->name = $category_name;
                    $category->description = $cat['description'];
                    $category->slug = Str::slug($category_name);
                    $category->save();
                    $result[] = $category;
                    $status = 201;
                }
                $response = [
                    'success' => true,
                    'message' => "Catégories créées avec succès",
                    'result' => $result
                ];
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la création des catégories",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * updateCategory function
     * Modifie une catégorie
     * @param Request $request
     * @return JsonResponse $response
     */
    public function updateCategory(Request $request)
    {
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(), [
                'category_id' => 'required|exists:categories,id',
                'name' => 'required|string',
            ]);
            if ($validate->fails()) {
                $response = [
                    'success' => false,
                    'message' => "Echec de validation des données",
                    'result' => null,
                    'errors' => $validate->getMessageBag()
                ];
            } else {
                $category = Category::find($request->category_id);
                $category->name = htmlspecialchars(strtoupper($request->name));
                $category->description = $request->description;
                $category->slug = Str::slug($request->name);
                $category->save();

                $response = [
                    'success' => true,
                    'message' => "Catégorie modifiée avec succès",
                    'result' => $category
                ];
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la modification de la catégorie",
                'result' => null,
                'errors' => $th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }
}
