<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('controls', function (Blueprint $table) {
            $table->id();
            $table->integer('agency_id');
            $table->integer('supervisor_id')->comment("Le superviseur, users");
            $table->integer('collector_id');
            $table->integer('client_id');
            $table->enum('status',['approved','blocked'])->default('approved');
            $table->integer('subscription_id');
            $table->timestamp('date_control')->nullable();
            $table->timestamp('date_start_control')->nullable();
            $table->timestamp('date_end_control')->nullable();
            $table->text('reason')->nullable();
            $table->float('total_dette')->nullable();
            $table->float('total_payment')->nullable();
            $table->json('payments')->nullable()->comment("Les faux paiements du client du collecteur envers l'agence");
            $table->json('dettes')->nullable()->comment("Les dettes du client du collecteur envers l'agence");
            $table->text('description')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('controls');
    }
};
