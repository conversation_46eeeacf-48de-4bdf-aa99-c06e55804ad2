<?php

namespace App\Http\Controllers\API\agence;

use App\Http\Controllers\API\agence\AgencyController;
use App\Http\Controllers\Controller;
use App\Models\Control;
use App\Models\Personal;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SupervisorController extends AgencyController
{
    /**
     * get_collectors function
     * Get all collectors of the agency
     * @param Request $request
     * @return void
     */
    public function get_collectors(Request $request){
        $response = [];
        $status = 200;
        try {
            $supervisor = $this->get_personal_auth($request);
            $agency = $supervisor->currentAgency()->first();
            $collectors = $agency->currentCollectors()->with([
                'city','quarter','clients','controls'
            ])->orderBy('created_at','desc')->get();
            $response = [
                'success'=>true,
                'message'=>"Liste des collecteurs de l'agence ".$agency->name,
                'result'=>$collectors
            ];
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la récupération des collecteurs de l'agence",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * get_collector_detail function
     * Get detail of the collector
     * @param Request $request
     * @return JsonResponse $response
     */
    public function get_collector_detail(Request $request){
        $response = [];
        $status = 200;
        try{
            $validate = Validator::make($request->all(),[
                'collector_id'=>'required',
            ]);
            if($validate->fails()){
                $response = [
                    'success'=>false,
                    'message'=>"Echec de validation des données",
                    'result'=>null,
                    'errors'=>$validate->errors(),
                ];
            }else{
                // $supervisor = $this->get_personal_auth($request);
                $collector = Personal::where('id',$request->collector_id)->with([
                    'city','quarter','clients','cotisations','subscriptions','versements',
                    'controls'=>function($query){
                        $query->with(['subscription','collector','client','agency'])->orderBy('created_at','desc');
                    }
                ])->whereId($request->collector_id)->first();
                $response = [
                    'success'=>true,
                    'message'=>"Détail collecteur",
                    'result'=>$collector
                ];

            }
        }catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la récupération des collecteurs de l'agence",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * get_collector_clients function
     * Get all clients of the collector
     * @param Request $request
     * @return void
     */
    public function get_collector_clients(Request $request){
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(),[
                'collector_id'=>'required|numeric'
            ]);
            if ($validate->fails()) {
                $response = [
                    'success'=>false,
                    'message'=>"Echec de validation des données",
                    'result'=>null,
                    'errors'=>$validate->getMessageBag()
                ];
            } else {
                $collector = $this->get_personal_by_id($request->collector_id);
                if ($collector == null) {
                    $response = [
                        'success'=>false,
                        'message'=>"Echec de validation des données",
                        'result'=>null,
                        'errors'=>"Collecteur introuvable"
                    ];
                } else {
                    $clients = $collector->clients()->with([
                        'city','quarter','cotisations','subscriptions',
                        'cotisations'=>function($query){
                            $query->with(['subscription','collector','payments','agency']);
                        },
                        'subscriptions'=>function($query){
                            $query->with(['pack','collector','client','payments','cotisation']);
                        },
                        'controls'=>function($query){
                            $query->orderBy('created_at','desc');
                        }
                    ])->orderBy('created_at','desc')->get();
                    $response = [
                        'success'=>true,
                        'message'=>"Liste des clients du collecteur",
                        'result'=>$clients
                    ];
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la récupération des clients du collecteur",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * get_detail_subscription function
     * Get detail of subscription
     * @param Request $request
     * @return JsonResponse
     */
    public function get_detail_subscription(Request $request){
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(),[
                'subscription_id'=>'required|numeric'
            ]);
            if ($validate->fails()) {
                $response = [
                    'success'=>false,
                    'message'=>"Echec de validation des données",
                    'result'=>null,
                    'errors'=>$validate->getMessageBag()
                ];
            } else {
                $subscription = $this->get_subscription_by_id($request->subscription_id);
                if ($subscription == null) {
                    $response = [
                        'success'=>false,
                        'message'=>"Echec de validation des données",
                        'result'=>null,
                        'errors'=>"Abonnement introuvable"
                    ];
                } else {
                    $response = [
                        'success'=>true,
                        'message'=>"Détail de l'abonnement",
                        'result'=>$subscription
                    ];
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la récupération du détail de l'abonnement",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * verify_client_subscription function
     * Verify client subscription
     * @param Request $request
     * @return void
     */
    public function verify_client_subscription(Request $request){
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(),[
                'client_id'=>'nullable|numeric',
                'subscription_id'=>'required|numeric',
                'date_start'=>'required|date|date_format:Y-m-d',
                'date_end'=>'required|date|date_format:Y-m-d'
            ]);
            if ($validate->fails()) {
                $response = [
                    'success'=>false,
                    'message'=>"Echec de validation des données",
                    'result'=>null,
                    'errors'=>$validate->getMessageBag()
                ];
            } else {
                $subscription = $this->get_subscription_by_id($request->subscription_id);
                if ($subscription == null) {
                    $response = [
                        'success'=>false,
                        'message'=>"Echec de validation des données",
                        'result'=>null,
                        'errors'=>"Abonnement introuvable"
                    ];
                } else {
                    $payments = $subscription->payments()->whereBetween('payment_date',[$request->date_start,$request->date_end])->get();
                    $response = [
                        'success'=>true,
                        'message'=>"Vérification de l'abonnement du client",
                        'result'=>[
                            'subscription'=>$subscription,
                            'payments'=>$payments
                        ]
                    ];
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la vérification de l'abonnement du client",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * make_control_client function
     * Make control of client
     * @param Request $request
     * @return JsonResponse
     */
    public function make_control_client(Request $request){
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(),[
                'client_id'=>'required|numeric',
                'subscription_id'=>'required|numeric',
                'date_start_control'=>'required|date|date_format:Y-m-d',
                'date_end_control'=>'required|date|date_format:Y-m-d',
                'payments'=>'nullable|array',
                'dettes'=>'nullable|array',
                'status'=>'required'
            ]);
            if ($validate->fails()) {
                $response = [
                    'success'=>false,
                    'message'=>"Echec de validation des données",
                    'result'=>null,
                    'errors'=>$validate->getMessageBag()
                ];
            } else {
                $supervisor = $this->get_personal_auth($request);
                $date_start_control = $request->date_start;
                $date_end_control = $request->date_end_control;

                $check_control = Control::where('client_id',$request->client_id)
                    ->where('subscription_id',$request->subscription_id)
                    ->whereBetween('date_control',[$date_start_control,$date_end_control])
                ->first();
                if ($check_control !== null) {
                    $response = [
                        'success'=>false,
                        'message'=>"Echec, ce client a déjà été contrôlé pour cette période",
                        'result'=>null,
                        'errors'=>"Ce client a déjà été contrôlé"
                    ];
                } else {
                    $subscription = Subscription::where('id',$request->subscription_id)->where('client_id',$request->client_id)->first();
                    if ($subscription == null) {
                        $response = [
                            'success'=>false,
                            'message'=>"Echec, carnet introuvable",
                            'result'=>null,
                            'errors'=>"Abonnement introuvable"
                        ];
                    } else {
                        /**
                         * $dettes = [
                         *    ['payment_date'=>'2021-01-01','amount'=>1000],
                         * ];
                         */
                        $dettes = $request->dettes;
                        $payments = $request->payments;
                        $total_dette = 0;
                        $total_payment = 0;
                        $check_payment = false;
                        $check_dette = false;
                        if (isset($request->dettes)) {
                            $check_dette = $this->validate_dette($dettes);

                            if ($check_dette == true) {
                                # calcul du montant total dette
                                foreach ($dettes as $dette) {
                                    $total_dette += $dette['amount'];
                                }
                            } else {
                                $response = [
                                    'success'=>false,
                                    'message'=>"Echec de validation des données",
                                    'result'=>null,
                                    'errors'=>"Dette invalide"
                                ];
                            }
                        }

                        if(isset($request->payments)){
                            $check_payment =  $this->validate_payment($payments);
                            if ($check_payment == true) {
                                # calcul du montant total payment
                                foreach ($payments as $payment) {
                                    $total_payment += $payment['amount'];
                                }
                            } else {
                                $response = [
                                    'success'=>false,
                                    'message'=>"Echec de validation des données",
                                    'result'=>null,
                                    'errors'=>"Paiement invalide"
                                ];
                            }
                        }
                        $control = $supervisor->controls()->create([
                            'subscription_id'=>$subscription->id,
                            'date_start_control'=>$date_start_control,
                            'date_end_control'=>$date_end_control,
                            'total_dette'=>$total_dette,
                            'total_payment'=>$total_payment,
                            'status'=>$request->status,
                            'payments'=>$payments,
                            'dettes'=>$dettes,
                            'client_id'=>$subscription->client_id,
                            'collector_id'=>$subscription->collector_id
                        ]);
                        if ($control == null) {
                            $response = [
                                'success'=>false,
                                'message'=>"Echec de création du contrôle",
                                'result'=>null,
                                'errors'=>"Echec de la création du contrôle"
                            ];
                        } else {
                            //bloquer le collecteur si status !="approved"
                            $response = [
                                'success'=>true,
                                'message'=>"Vérification de l'abonnement du client",
                                'result'=>[
                                    'subscription'=>$subscription,
                                    'control'=>$control,
                                ]
                            ];
                        }

                    }
                }

            }
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la vérification de l'abonnement du client",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * get_controls function
     * Get controls of supervisor
     * @param Request $request
     * @return void
     */
    public function get_controls(Request $request){
        $response = [];
        $status = 200;
        try {
            $supervisor = $this->get_personal_auth($request);
            if ($supervisor == null) {
                $response = [
                    'success'=>false,
                    'message'=>"Echec de récupération des contrôles",
                    'result'=>null,
                    'errors'=>"Superviseur introuvable"
                ];
            } else {
                $controls = $supervisor->controls()->with([
                    'client'=>function($query){
                        $query->select('id','nom','prenoms','phone');
                    },
                    'collector'=>function($query){
                        $query->select('id','nom','prenoms','phone');
                    },
                    'subscription'=>function($query){
                        $query->with([
                            'client'=>function($query){
                                $query->select('id','nom','prenoms','phone');
                            },
                            'collector'=>function($query){
                                $query->select('id','nom','prenoms','phone');
                            },
                            'pack'=>function($query){
                                $query->select('id','name','total_price');
                            }
                        ])->get();
                    }
                ])->orderBy('created_at','desc')->get();
                $response = [
                    'success'=>true,
                    'message'=>"Récupération des contrôles",
                    'result'=>$controls
                ];
            }
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de récupération des contrôles",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * validate_dette function
     * Validate dette
     * @param array $dettes
     * @return bool
     */
    public function validate_dette(array $dettes): bool {
        $validate = true;
        foreach ($dettes as $dette) {
            if (!isset($dette['payment_date']) || !isset($dette['amount'])) {
                $validate = false;
                break;
            }
        }
        return $validate;
    }

    /**
     * validate_payment function
     * Validate payment
     * @param array $payments
     * @return bool
     */
    public function validate_payment(array $payments): bool {
        $validate = true;
        foreach ($payments as $payment) {
            if (!isset($payment['payment_id']) && !isset($payment['amount'])) {
                $validate = false;
                break;
            }
        }
        return $validate;
    }


}
