<?php

namespace App\Exceptions;

use Throwable;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;

class Hand<PERSON> extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    /**
     * Convert an authentication exception into a response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Illuminate\Auth\AuthenticationException  $exception
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function unauthenticated($request, AuthenticationException $exception) {
        // for api and web request
        return response()->json([
            'success' => false,
            'message' => "Vous n'êtes pas autorisé"
        ], 401);
    }

    public function render($request, Throwable $exception) {
        // route not found exception
        if ($exception instanceof \Symfony\Component\HttpKernel\Exception\NotFoundHttpException) {
            // web custom response
            if ($request->is('api/*')) {
                return response()->json([
                    'success' => false,
                    'message' => "Cette route n'existe pas"
                ], 404);
            }
            else {
                // web custom response
                return response()->json([
                    'success' => true,
                    'message' => "BIENVENUE SUR BENILIFE :)"
                ], 200);
            }
        }
        // method not allowed exception
        if ($exception instanceof \Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException) {
            return response()->json([
                'success' => false,
                'message' => "Cette méthode n'est pas autorisée"
            ], 405);
        }
        // route not found exception for web and api
        // if($exception instanceof \Symfony\Component\Routing\Exception\RouteNotFoundException){

        // }
        return parent::render($request, $exception);
    }
}
