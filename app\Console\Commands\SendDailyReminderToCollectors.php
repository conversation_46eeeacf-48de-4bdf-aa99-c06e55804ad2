<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Services\FirebaseService; // Assurez-vous que votre service Firebase est importé ici
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;


class SendDailyReminderToCollectors extends Command
{
    protected $signature = 'notify:collectors-reminder';
    protected $description = "Envoie un rappel aux collecteurs de faire leur versement en fin de journée";

    protected $firebaseService;

    public function __construct(FirebaseService $firebaseService)
    {
        parent::__construct();
        $this->firebaseService = $firebaseService;
    }

    public function handle()
    {
        Log::info("Task [ReminderNotificationsTask] started.");
        $title = "Rappel";
        $body = "N'oubliez pas de faire votre versement en fin de journée";

        // Récupérer les tokens des collecteurs actifs (adaptez la logique de filtre si nécessaire)
        $collectors = User::whereRoleId(6)->whereNotNull('device_token')->pluck('device_token')->toArray();

        if (!empty($collectors)) {
            $this->firebaseService->sendNotifications($collectors, $title, $body);
            $this->info('Notifications envoyées aux collecteurs.');
        } else {
            $this->info('Aucun collecteur actif avec token pour la notification.');
        }
        Log::info("Task [ReminderNotificationsTask] finished.");
    }
}
