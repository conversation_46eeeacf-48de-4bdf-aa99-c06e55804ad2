<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->json('metadata')->nullable()->after('month')->comment("[{id,key,month,amount,payment_date,versement_id}]");
            $table->double('amount',12,2)->comment("Pour une cotisation, ça represente le montant cumul du mois en cours, sinon le prix de la vente du carnet")->change();
            // $table->dropColumn('versement_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->dropColumn('metadata');
            // $table->integer('versement_id')->nullable();
        });
    }
};
