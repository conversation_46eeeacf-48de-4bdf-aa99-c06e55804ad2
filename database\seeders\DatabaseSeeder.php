<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

use App\Models\Role;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call(RoleTableSeeder::class);
        $this->call(CategoryTableSeeder::class);
        $this->call(CountryTableSeeder::class);
        // $this->call(CityTableSeeder::class);
        // $this->call(QuarterTableSeeder::class);
        // $this->call(UsersTableSeeder::class);
        $this->call(AppConfigurationTableSeeder::class);
    }
}
