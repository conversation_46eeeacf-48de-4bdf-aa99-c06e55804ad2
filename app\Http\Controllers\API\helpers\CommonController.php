<?php

namespace App\Http\Controllers\API\helpers;

use App\Http\Controllers\Controller;
use App\Models\City;
use App\Models\Country;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\User;
use App\Models\AppConfiguration;

class CommonController extends HelperController
{
    public function get_all_countries(Request $request){
        $response = [];
        $status = 200;
        try {
            $countries = Country::orderBy('id', 'desc')->withCount(['cities'])->get();
            $response = [
                'success'=>true,
                'message'=>"Liste des pays",
                'result'=>$countries
            ];
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la récupération des pays",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * get_all_cities function
     *  Récupère la liste des villes
     * @param Request $request
     * @return void
     */
    public function get_all_cities(Request $request){
        $response = [];
        $status = 200;
        try {
            $cities = City::with([
                'country',
                'quarters'
            ])->get();
            $response = [
                'success'=>true,
                'message'=>"Liste des villes",
                'result'=>$cities
            ];
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la récupération des villes",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * get_all_quarters function
     * Récupère la liste des quartiers
     * @param Request $request
     * @return void
     */
    public function get_all_quarters(Request $request){
        $response = [];
        $status = 200;
        try {
            $quarters = $this->get_quarters();
            $response = [
                'success'=>true,
                'message'=>"Liste des quartiers",
                'result'=>$quarters
            ];
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la récupération des quartiers",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * get_quarters_by_city function
     *
     * @param Request $request
     * @return void
     */
    public function get_city_quarters(Request $request){
        $response = [];
        $status = 200;
        try {
            $validate = Validator::make($request->all(),[
                'city_id'=>'required|numeric'
            ]);
            if ($validate->fails()) {
                $response = [
                    'success'=>false,
                    'message'=>"Echec de validation des données",
                    'result'=>null,
                    'errors'=>$validate->getMessageBag()
                ];
            } else {
                $city = City::where('id',$request->city_id)->first();
                if ($city == null) {
                    $response = [
                        'success'=>false,
                        'message'=>"Echec de validation des données",
                        'result'=>null,
                        'errors'=>"Ville introuvable"
                    ];
                } else {
                    $quarters = $city->quarters()->get();
                    $response = [
                        'success'=>true,
                        'message'=>"Liste des quartiers de la ville",
                        'result'=>$quarters
                    ];
                }
            }
        } catch (\Throwable $th) {
            $response = [
                'success'=>false,
                'message'=>"Echec de la récupération des quartiers de la ville",
                'result'=>null,
                'errors'=>$th->getMessage()
            ];
            $status = 500;
        }
        return $this->apiResponse($response,$status);
    }

    /**
     * get_active_connected_users_device_tokens_by_roles function
     *
     * @param array $roles
     * @param array $without_users
     * @return array $response
     */
    public function get_active_connected_users_device_tokens_by_roles($roles = [6], $without_users = [])
    {
        $response = [];
        try {
            $query = User::where('status', '=', 1)
                ->where('is_online', '=', 1)
                ->whereIn('role_id', $roles)
                ->where('device_token', '!=', null);

            if (!empty($without_users)) {
                $query->whereNotIn('id', $without_users);
            }

            $device_tokens = $query->pluck('device_token')->toArray();

            $response = [
                'success' => true,
                'message' => "Liste des tokens des utilisateurs connectés",
                'result' => $device_tokens
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération des tokens des utilisateurs connectés",
                'result' => null,
                'errors' => $th->getMessage()
            ];
        }
        return $response;
    }

    public function get_app_configuration(Request $request)
    {
        $response = [];
        $status = 200;

        $app_type = $request->query('app_type');
        $app_configuration = AppConfiguration::where('app_type', $app_type)->first();
        if ($app_configuration !== null) {
            $response = [
                'success' => true,
                'message' => "Configuration de l'application",
                'result' => $app_configuration
            ];
        } else {
            $response = [
                'success' => false,
                'message' => "Echec de la récupération de la configuration de l'application",
                'result' => null,
                'errors' => null
            ];
        }
        return $this->apiResponse($response, $status);
    }

}
