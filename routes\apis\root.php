<?php

use App\Models\Personal;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::group(['namespace' => "root", 'prefix' => 'root'], function () {


    Route::post('uuid', function () {
        $personals = Personal::all();
        foreach ($personals as $personal) {
            $uuid = Str::uuid();
            $personal->uuid = $uuid;
            $personal->save();
        }
        return response()->json([
            'success' => true,
            'message' => 'UUID générés',
            'result' => $personals
        ]);
    });

    Route::post('/update-existing-records', 'RootController@updateExistingRecords');
    Route::get('refac-existing-payments', 'RootController@refacExistingCotisationsPayments');
    Route::get('incorrect-payments', 'RootController@getCollectorPaymentIncorrectV2');
    Route::get('refact-secret-code', 'RootController@addUserSecretCode');
    Route::get('add-cotisation-id-to-collect-payments', 'RootController@addCotisationIdToCollectPayments');
    Route::get('refac-collect-payments', 'RootController@refacCollectPayments');
    Route::get('reset-subscriptions-code', 'RootController@resetExistingSubscriptionsCode');

    Route::prefix('migrations')->group(function(){
        Route::get('/personals','DataMigrationController@migratePersonals');
        Route::get('/clients','DataMigrationController@migrateClientData');
        Route::get('/check-collector-exist','DataMigrationController@checkCollectorExist');
        Route::get('/new-collectors-id','DataMigrationController@getNewCollectorsId');
        Route::get('/new-agencies-id','DataMigrationController@getNewAgenciesId');
        Route::get('/packs','DataMigrationController@migratePacks');
        Route::get('/subscriptions','DataMigrationController@migrateClientSubscriptions');
        Route::get('/cotisations','DataMigrationController@migrateClientCotisations');
        Route::get('/payments','DataMigrationController@migratePaymentData');
        Route::get('/wallets','DataMigrationController@updatePersonalWalletBalance');
    });


});
