<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
            $table->integer('agency_id')->nullable();
            $table->integer('pack_id');
            $table->integer('client_id');
            $table->integer('collector_id')->nullable();
            $table->float('price');
            $table->enum('status',['pending','started','finished','canceled','delivered'])->default('pending');
            $table->text('motif')->nullable();
            $table->date('started_at')->nullable();
            $table->date('finished_at')->nullable();
            $table->date('delivered_at')->nullable();
            $table->string('code')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};
