<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Versement extends Model
{
    use HasFactory;
    protected $fillable = [
        'collector_id','cashier_id','amount','payment_date','status','agency_id','qrcode','expired_at',
        'token','amount_remaining','confirmed_at','payment_mode','trx_ref'
    ];

    public function getCreatedAtAttribute($value) {
        return \Carbon\Carbon::parse($value)->diffForHumans();
    }

    public function collector()
    {
        return $this->belongsTo(Personal::class, 'collector_id','id');
    }

    public function cashier()
    {
        return $this->belongsTo(Personal::class, 'cashier_id','id');
    }

    public function agency()
    {
        return $this->belongsTo(Agency::class, 'agency_id');
    }


}
