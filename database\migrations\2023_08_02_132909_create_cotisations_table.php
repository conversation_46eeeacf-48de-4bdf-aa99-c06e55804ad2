<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cotisations', function (Blueprint $table) {
            $table->id();
            $table->integer('agency_id');
            $table->integer('client_id');
            $table->integer('collector_id');
            $table->integer('subscription_id');
            $table->float('total_amount');
            $table->integer('nbre_cotise')->nullable();
            $table->datetime('date_cotise')->comment("Dernière date de cotisation");
            $table->string('month')->nullable();
            $table->timestamp('start_at')->nullable();
            $table->timestamp('end_at')->nullable();
            $table->string('status')->default('started')->comment("Status de la cotisation (started, in_progress, finished)");
            $table->text('description')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cotisations');
    }
};
