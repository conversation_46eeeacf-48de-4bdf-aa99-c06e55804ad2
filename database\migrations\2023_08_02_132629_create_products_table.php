<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->integer('category_id')->nullable();
            $table->string('name');
            $table->text('description')->nullable();
            $table->integer('stock_quantity')->default(0);
            $table->float('price_achat')->default(0);
            $table->float('price_vente')->default(0);
            $table->text('image')->nullable();
            $table->integer('status')->default(1);
            $table->string('code')->nullable();
            $table->integer('alert_quantity')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
