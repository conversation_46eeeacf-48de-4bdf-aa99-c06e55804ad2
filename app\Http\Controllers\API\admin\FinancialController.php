<?php
namespace App\Http\Controllers\API\admin;

use Carbon\Carbon;
use App\Models\Cotisation;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\API\helpers\HelperController;

class FinancialController extends HelperController
{
    public function getFinancialHealth()
    {
        try {
            // 1. Calcul des données de base
            $baseData = [
                'cash_available'        => Cotisation::whereNotIn('status', ['finished'])->sum('total_amount'),
                'financial_commitments' => Subscription::where('subscriptions.status', 'delivered')
                    ->join('packs', 'subscriptions.pack_id', '=', 'packs.id')
                    ->sum(DB::raw('packs.total_price * 0.8')),
                'gross_revenue'         => Cotisation::where('status', 'finished')->sum('total_amount'),
            ];

            // 2. Calcul des données dérivées
            $netRevenue    = $baseData['gross_revenue'] - $baseData['financial_commitments'];
            $grossRevenue  = $baseData['gross_revenue'] ?: 1; // Évite division par zéro
            $cashAvailable = $baseData['cash_available'] ?: 1;

            $derivedData = [
                'net_revenue'     => $netRevenue,
                'gross_margin'    => ($netRevenue / $grossRevenue) * 100,
                'liquidity_ratio' => $baseData['financial_commitments'] / $cashAvailable,
            ];

            // 3. Formatage des résultats
            $result = [
                'financial_data' => array_merge($baseData, $derivedData),
                'health_status'  => $this->evaluateFinancialHealth($derivedData),
                'last_updated'   => now()->format('Y-m-d H:i:s'),
            ];

            return $this->apiResponse([
                'success' => true,
                'message' => "Santé financière analysée avec succès",
                'result'  => $result,
            ]);
        } catch (\Exception $e) {
            Log::error("Erreur analyse financière: " . $e->getMessage());

            return $this->apiResponse([
                'success' => false,
                'message' => 'Erreur de calcul financier',
                'error'   => env('APP_DEBUG') ? $e->getMessage() : 'Une erreur est survenue',
            ], 500);
        }
    }

    private function calculateContentCost()
    {
        return Subscription::where('subscriptions.status', 'delivered')
            ->join('packs', 'subscriptions.pack_id', '=', 'packs.id')
            ->sum(DB::raw('packs.total_price * 0.8'));
    }

    private function evaluateFinancialHealth($data)
    {
        $status   = 'healthy';
        $messages = [];

        if ($data['net_revenue'] < 0) {
            $status     = 'critical';
            $messages[] = 'Attention: Votre entreprise est en déficit!';
        }

        if ($data['liquidity_ratio'] > 0.7) {
            $status     = ($status === 'healthy') ? 'warning' : $status;
            $messages[] = 'Votre ratio de liquidité est élevé. Surveillez vos engagements.';
        }

        if ($data['gross_margin'] < 20) {
            $status     = ($status === 'healthy') ? 'warning' : $status;
            $messages[] = 'Marge brute faible. Pensez à optimiser vos coûts.';
        }

        return [
            'status'          => $status,
            'messages'        => empty($messages) ? ['Votre santé financière est bonne'] : $messages,
            'recommendations' => $this->generateRecommendations($status),
        ];
    }

    private function generateRecommendations($status)
    {
        return match ($status) {
            'critical' => [
                'Réduire immédiatement les engagements',
                'Augmenter les encaissements',
                'Audit financier urgent',
            ],
            'warning' => [
                'Renégocier les termes avec les fournisseurs',
                'Optimiser la rotation des cotisations',
                'Diversifier les sources de revenus',
            ],
            default => [
                'Maintenir la croissance actuelle',
                'Constituer des réserves pour 3 mois',
                "Investir dans l'amélioration des processus",
            ]
        };
    }

    public function getFinancialTrends(Request $request)
    {
        $validated = $request->validate([
            'period' => 'required|in:today,week,month,year',
            'year'   => 'nullable|digits:4',
            'month'  => 'nullable|between:1,12', // Ajout du mois pour le cas 'month'
        ]);

        try {
            $year  = $validated['year'] ?? date('Y');
            $month = $validated['month'] ?? null;
            $data  = [];

            switch ($validated['period']) {
                case 'today':
                    $data = $this->getTodayData($year);
                    break;
                case 'week':
                    $data = $this->getWeekData($year);
                    break;
                case 'month':
                    $data = $this->getMonthData($year, $month);
                    break;
                case 'year':
                    $data = $this->getYearData($year);
                    break;
            }

            return $this->apiResponse([
                'success' => true,
                'message' => "Données de tendance financière récupérées avec succès",
                'result'  => $data,
                'errors'  => null,
            ]);
        } catch (\Exception $e) {
            return $this->apiResponse([
                'success' => false,
                'message' => 'Erreur de récupération des données',
                'result'  => null,
                'errors'  => $e->getMessage(),
            ], 500);
        }
    }

    private function getTodayData($year)
    {
        $date = now(); // Par défaut, on utilise la date actuelle
        if ($year != date('Y')) {
            // Si une année différente est spécifiée, on prend une date arbitraire dans cette année
            // (car les données "today" n'ont de sens que pour l'année en cours)
            $date = Carbon::create($year, 1, 1);
        }

        $labels = [];
        for ($i = 0; $i <= 24; $i += 3) {
            $labels[] = sprintf('%02d:00', $i);
        }

        $subscriptionsData = Subscription::whereDate('created_at', $date->format('Y-m-d'))
            ->selectRaw('HOUR(created_at) as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('count', 'hour')
            ->toArray();

        $cotisationsData = Cotisation::whereDate('created_at', $date->format('Y-m-d'))
            ->selectRaw('HOUR(created_at) as hour, SUM(total_amount) as total')
            ->groupBy('hour')
            ->orderBy('hour')
            ->pluck('total', 'hour')
            ->toArray();

        $subscriptions = [];
        $cotisations   = [];
        $revenus       = [];

        foreach (range(0, 23, 3) as $hour) {
            $subscriptions[] = $subscriptionsData[$hour] ?? 0;
            $cotisations[]   = $cotisationsData[$hour] ?? 0;
            $revenus[]       = $cotisationsData[$hour] ?? 0;
        }

        return [
            'labels'        => $labels,
            'subscriptions' => $subscriptions,
            'cotisations'   => $cotisations,
            'revenus'       => $revenus,
        ];
    }

    private function getWeekData($year)
    {
        // Pour une année spécifique, on prend la première semaine de janvier
        $startDate = $year != date('Y')
        ? Carbon::create($year, 1, 1)->startOfWeek()
        : now()->startOfWeek();

        $endDate = clone $startDate;
        $endDate->endOfWeek();

        $labels = ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'];

        $subscriptionsData = Subscription::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DAYOFWEEK(created_at) as day, COUNT(*) as count')
            ->groupBy('day')
            ->orderBy('day')
            ->pluck('count', 'day')
            ->toArray();

        $cotisationsData = Cotisation::whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DAYOFWEEK(created_at) as day, SUM(total_amount) as total')
            ->groupBy('day')
            ->orderBy('day')
            ->pluck('total', 'day')
            ->toArray();

        $subscriptions = [];
        $cotisations   = [];
        $revenus       = [];

        foreach ([2, 3, 4, 5, 6, 7, 1] as $mysqlDay) {
            $subscriptions[] = $subscriptionsData[$mysqlDay] ?? 0;
            $cotisations[]   = $cotisationsData[$mysqlDay] ?? 0;
            $revenus[]       = $cotisationsData[$mysqlDay] ?? 0;
        }

        return [
            'labels'        => $labels,
            'subscriptions' => $subscriptions,
            'cotisations'   => $cotisations,
            'revenus'       => $revenus,
        ];
    }

    private function getMonthData($year, $month = null)
    {
        $month       = $month ?? date('m');
        $daysInMonth = date('t', mktime(0, 0, 0, $month, 1, $year));

        $labels = [];
        for ($day = 1; $day <= $daysInMonth; $day++) {
            $labels[] = str_pad($day, 2, '0', STR_PAD_LEFT);
        }

        $subscriptionsData = Subscription::whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->selectRaw('DAY(created_at) as day, COUNT(*) as count')
            ->groupBy('day')
            ->orderBy('day')
            ->pluck('count', 'day')
            ->toArray();

        $cotisationsData = Cotisation::whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->selectRaw('DAY(created_at) as day, SUM(total_amount) as total')
            ->groupBy('day')
            ->orderBy('day')
            ->pluck('total', 'day')
            ->toArray();

        $subscriptions = [];
        $cotisations   = [];
        $revenus       = [];

        for ($day = 1; $day <= $daysInMonth; $day++) {
            $subscriptions[] = $subscriptionsData[$day] ?? 0;
            $cotisations[]   = $cotisationsData[$day] ?? 0;
            $revenus[]       = $cotisationsData[$day] ?? 0;
        }

        return [
            'labels'        => $labels,
            'subscriptions' => $subscriptions,
            'cotisations'   => $cotisations,
            'revenus'       => $revenus,
        ];
    }

    private function getYearData($year)
    {
        $labels = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun', 'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'];

        $subscriptionsData = Subscription::whereYear('created_at', $year)
            ->selectRaw('MONTH(created_at) as month, COUNT(*) as count')
            ->groupBy('month')
            ->orderBy('month')
            ->pluck('count', 'month')
            ->toArray();

        $cotisationsData = Cotisation::whereYear('created_at', $year)
            ->selectRaw('MONTH(created_at) as month, SUM(total_amount) as total')
            ->groupBy('month')
            ->orderBy('month')
            ->pluck('total', 'month')
            ->toArray();

        $subscriptions = [];
        $cotisations   = [];
        $revenus       = [];

        for ($month = 1; $month <= 12; $month++) {
            $subscriptions[] = $subscriptionsData[$month] ?? 0;
            $cotisations[]   = $cotisationsData[$month] ?? 0;
            $revenus[]       = $cotisationsData[$month] ?? 0;
        }

        return [
            'labels'        => $labels,
            'subscriptions' => $subscriptions,
            'cotisations'   => $cotisations,
            'revenus'       => $revenus,
        ];
    }

}
