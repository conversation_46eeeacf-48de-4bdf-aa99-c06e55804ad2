<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('agency_personals', function (Blueprint $table) {
            $table->id();
            $table->integer('agency_id');
            $table->integer('personal_id');
            $table->integer('role_id');
            $table->boolean('is_current')->default(0);
            $table->timestamp('affected_at')->nullable();
            $table->timestamp('mutated_at')->nullable();
            $table->boolean('status')->default(1);
            // $table->unique(['agency_id','personal_id']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('agency_personals');
    }
};
