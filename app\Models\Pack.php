<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Pack extends Model
{
    use HasFactory;
    protected $fillable = [
        'name','description','total_price','status','duration','category','tarif','carnet_price'
    ];

    /**
     * The clients that belong to the Pack
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function clients(): BelongsToMany
    {
        return $this->belongsToMany(Client::class, 'subscriptions', 'client_id', 'pack_id')->withPivot(['started_at','finished_at','status','collector_id','agency_id','motif'])->withTimestamps();
    }

    /**
     * The products that belong to the Pack
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'pack_products', 'pack_id', 'product_id')->withPivot(['quantity', 'price', 'status'])->withTimestamps();
    }





}
