<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Quarter extends Model
{
    use HasFactory;

    protected $fillable = [
        'name','description','city_id',
    ];

    public function city()
    {
        return $this->belongsTo(City::class, 'city_id');
    }

    public function agencies()
    {
        return $this->hasMany(Agency::class, 'quarter_id');
    }

    public function personals()
    {
        return $this->hasMany(Personal::class, 'quarter_id');
    }

    public function clients()
    {
        return $this->hasMany(Client::class, 'quarter_id');
    }

}
