<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wallets', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id')->nullable()->comment("L'utilisateur qui possède le compte wallet");
            $table->integer('agency_id')->nullable()->comment("L'agence qui possède le compte wallet");
            $table->string('code');
            $table->double('balance')->default(0);
            $table->string('status')->default('active');
            $table->string('type')->default('agent');
            $table->string('currency')->default('XOF');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wallets');
    }
};
