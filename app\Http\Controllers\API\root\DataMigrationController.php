<?php

namespace App\Http\Controllers\API\root;

use App\Http\Controllers\API\helpers\HelperController;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class DataMigrationController extends HelperController
{
    /**
     * Get the matching agency ID from the JSON file by agency name.
     *
     * @param string $agencyName
     * @return int|null
     */
    public function getMatchingAgencyId(int $agencyId)
    {
        try {
            $filePath = base_path('/database/agencies.json');
            if (!file_exists($filePath)) {
                return null;
            }
            $agencies = json_decode(file_get_contents($filePath), true);

            if (!is_array($agencies)) {
                return null;
            }
            $newAgency = collect($agencies)->where('id', $agencyId)->first();
            if ($newAgency) {
                $agency = DB::table('agencies')->where('name', $newAgency['name'])->first();
                if ($agency) {
                    return $agency->id;
                } else {
                    return null;
                }
            }
        } catch (\Throwable $th) {
            // trow $th;
        }
    }

    public function getNewAgenciesId()
    {
        $response = [];
        try {
            $filePath = base_path('/database/agencies.json');
            if (!file_exists($filePath)) {
                return response()->json(['message' => 'Fichier JSON introuvable.'], 404);
            }
            $agencies = json_decode(file_get_contents($filePath), true);
            if (!is_array($agencies)) {
                return response()->json(['message' => 'Données JSON invalides.'], 400);
            }
            $Newagencies = collect($agencies);
            $existingAgencies = [];
            $newAgencies = [];
            foreach ($Newagencies as $newAgency) {
                $agency = DB::table('agencies')->where('name', $newAgency['name'])->first();
                if ($agency) {
                    $existingAgencies[] = [
                        'oldId' => $newAgency['id'],
                        'newId' => $agency->id,
                        'name' => $agency->name,
                    ];
                } else {
                    $newAgencies[] = [
                        'id' => $newAgency['id'],
                        'name' => $newAgency['name'],
                    ];
                }
            }
            $response = [
                'success' => true,
                'message' => 'Agencies ajoutés',
                'result' => [
                    'agencies' => $newAgencies,
                    'agenciesExist' => $existingAgencies
                ]
            ];
            return response()->json($response);
        } catch (\Throwable $th) {
            return response()->json(['message' => $th->getMessage()], 500);
        }
    }

    public function getMatchingCollectorId(int $collectorId)
    {
        // Vérification de l'existence du fichier JSON
        $filePath = base_path('/database/personals.json');
        if (!file_exists($filePath)) {
            Log::warning("Fichier personals.json introuvable");
            return null;
        }

        // Chargement et décodage des données JSON
        $personals = json_decode(file_get_contents($filePath), true);
        if (!is_array($personals)) {
            Log::warning("Données JSON invalides dans le fichier personals.json");
            return null;
        }

        // Recherche du collecteur correspondant dans le fichier JSON
        $collector = collect($personals)
            ->where('role_id', 6) // Filtrer uniquement les collecteurs
            ->firstWhere('id', $collectorId); // Trouver par ID

        if (!$collector || !isset($collector['nom'], $collector['prenoms'])) {
            return null; // Retourner null si le collecteur ou ses champs nécessaires n'existent pas
        }

        // Recherche du collecteur correspondant dans la base A
        $fetchedCollector = DB::table('personals')
            ->where('role_id', 6)
            ->where('nom', $collector['nom'])
            ->where('prenoms', $collector['prenoms'])
            ->orWhere('phone', $collector['phone'])
            ->first();

        return $fetchedCollector->id ?? null; // Retourner l'ID ou null si introuvable
    }

    public function getNewCollectorsId()
    {
        $response = [];
        try {
            $filePath = base_path('/database/personals.json');
            if (!file_exists($filePath)) {
                return response()->json(['message' => 'Fichier JSON introuvable.'], 404);
            }
            $personals = json_decode(file_get_contents($filePath), true);
            if (!is_array($personals)) {
                return response()->json(['message' => 'Données JSON invalides.'], 400);
            }
            $Newcollectors = collect($personals)->where('role_id', 6);
            $existingCollectors = [];
            $newPersonals = [];
            foreach ($Newcollectors as $newCollector) {
                $collector = DB::table('personals')
                    ->where('role_id', 6)
                    ->where('nom', $newCollector['nom'])
                    ->where('prenoms', $newCollector['prenoms'])
                    ->orWhere('phone', $newCollector['phone'])
                    ->first();
                if ($collector) {
                    $existingCollectors[] = [
                        'oldId' => $newCollector['id'],
                        'newId' => $collector->id,
                        'nom' => $collector->nom,
                        'prenoms' => $collector->prenoms,
                        'email' => $collector->email,
                        'phone' => $collector->phone,
                    ];
                } else {
                    $newPersonals[] = $newCollector;
                }
            }

            $response = [
                'success' => true,
                'message' => 'Collectors ajoutés',
                'result' => [
                    'personals' => $newPersonals,
                    'collectors' => $existingCollectors
                ]
            ];
            return response()->json($response);
        } catch (\Throwable $th) {
            return response()->json(['message' => $th->getMessage()], 500);
        }
    }

    public function checkCollectorExist()
    {
        $response = [];
        try {
            $filePath = base_path('/database/personals.json');
            if (!file_exists($filePath)) {
                return response()->json(['message' => 'Fichier JSON introuvable.'], 404);
            }
            $personals = json_decode(file_get_contents($filePath), true);
            if (!is_array($personals)) {
                return response()->json(['message' => 'Données JSON invalides.'], 400);
            }
            $Newcollectors = collect($personals)->where('role_id', 6);
            $existingCollectors = [];
            $newPersonals = [];
            foreach ($Newcollectors as $newCollector) {
                $exists = DB::table('personals')
                    ->where('role_id', 6)
                    ->where('nom', $newCollector['nom'])
                    ->where('prenoms', $newCollector['prenoms'])
                    ->orWhere('phone', $newCollector['phone'])
                    ->exists();
                if ($exists) {
                    $existingCollectors[] = $newCollector;
                } else {
                    $newPersonals[] = $newCollector;
                }
            }
            $response = [
                'success' => true,
                'message' => count($existingCollectors) . " collecteurs existants vérifiés",
                'result' => [
                    'totalNewCollectors' => $Newcollectors->count(),
                    'existingCollectorsCount' => count($existingCollectors),
                    'existingCollectors' => $existingCollectors,
                    'newPersonals' => $newPersonals
                ]
            ];
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => $th->getMessage(),
                'result' => null,
            ];
        }
        return response()->json($response, 200);
    }


    public function migratePersonals()
    {
        $response = [];
        try {
            $filePath = base_path('/database/personals.json');
            if (!file_exists($filePath)) {
                return response()->json(['message' => 'Fichier JSON introuvable.'], 404);
            }

            $personals = json_decode(file_get_contents($filePath), true);

            if (!is_array($personals)) {
                return response()->json(['message' => 'Données JSON invalides.'], 400);
            }

            $migratedCount = 0;
            $existedCount = 0;
            $existedData = [];
            DB::beginTransaction();
            try {
                foreach ($personals as $personal) {

                    $existingPersonal = DB::table('personals')
                        ->where('nom', $personal['nom'] ?? '')
                        ->where('prenoms', $personal['prenoms'] ?? '')
                        ->orWhere('phone', $personal['phone'] ?? '')
                        ->orWhere('email', $personal['email'] ?? '')
                        ->first();

                    if (!$existingPersonal) {
                        // Migrer le personnel dans la base A
                        //create user data
                        $username = $personal['nom'] . ' ' . $personal['prenoms'];
                        $password = Hash::make($personal['phone']);
                        $user = User::where('username', $username)->where('phone', $personal['phone'])->first();
                        $secret_code = rand(1000, 9999);
                        $userId = null;
                        if ($user !== null) {
                            $userId = $user->id;
                        } else {
                            $userId = DB::table('users')->insertGetId([
                                'username' => $username,
                                'password' => $password,
                                'phone' => $personal['phone'],
                                'email' => $personal['email'],
                                'role_id' => $personal['role_id'],
                                'status' => 1,
                                'secret_code' => Crypt::encryptString($secret_code)
                            ]);
                        }

                        $personalId = DB::table('personals')->insertGetId([
                            'nom' => $personal['nom'] ?? null,
                            'prenoms' => $personal['prenoms'] ?? null,
                            'role_id' => $personal['role_id'] ?? null,
                            'email' => $personal['email'] ?? null,
                            'phone' => $personal['phone'] ?? null,
                            'quarter_id' => $personal['quarter_id'] ?? null,
                            'user_id' => $userId,
                            'status' => $personal['status'] ?? 'active',
                            'created_at' => $personal['created_at'] ?? now(),
                            'updated_at' => now(),
                        ]);
                        $agencyPersonal = DB::connection('izinew')->table('agency_personals')->where('personal_id', $personal['id'])->first();
                        Log::info('agencyPersonal' . json_encode($agencyPersonal));
                        if ($agencyPersonal) {
                            $newAgencyId = $this->getMatchingAgencyId($agencyPersonal->agency_id);
                            if ($newAgencyId) {
                                DB::table('agency_personals')->insert([
                                    'agency_id' => $newAgencyId,
                                    'personal_id' => $personalId,
                                    'is_current' => 1,
                                    'role_id' => $personal['role_id'] ?? null,
                                    'created_at' => $personal['created_at'] ?? now(),
                                    'updated_at' => now(),
                                ]);
                                $migratedCount++;
                            }
                        }
                    } else {
                        $existedCount++;
                        $agencyPersonal = DB::table('agency_personals')->where('personal_id', $personal['id'])->first();
                        $existedData[] = [
                            'id' => $personal['id'],
                            'nom' => $personal['nom'],
                            'prenoms' => $personal['prenoms'],
                            'phone' => $personal['phone'],
                            'user_id' => $personal['user_id'],
                            'agency_id' => $agencyPersonal->agency_id,
                        ];
                    }
                }
                DB::commit();
                $response = [
                    'success' => true,
                    'message' => "{$migratedCount} personnels migrés avec succès.",
                    'result' => [
                        'migratedCount' => $migratedCount,
                        'existedCount' => $existedCount,
                        'existedData' => $existedData
                    ]
                ];
                return response()->json($response, 201);
            } catch (\Throwable $th) {
                DB::rollBack();
                $response = [
                    'success' => false,
                    'message' => $th->getMessage(),
                    'result' => null,
                ];
                return response()->json($response);
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => $th->getMessage(),
                'result' => null,
            ];
            return response()->json($response, 500);
        }
    }


    public function migrateClientData()
    {
        set_time_limit(10000);
        ini_set('memory_limit', '4048M');
        ini_set('output_buffering', '4096');
        $response = [];
        $clientfile = base_path('/database/clients.json'); // Chemin du fichier JSON

        // Vérifier si le fichier existe
        if (!file_exists($clientfile)) {
            return response()->json(['success' => false, 'message' => 'Fichier clients.json introuvable'], 404);
        }

        $clients = json_decode(file_get_contents($clientfile), true);

        // Vérifier si les données sont valides
        if (!is_array($clients)) {
            return response()->json(['success' => false, 'message' => 'Données JSON invalides'], 400);
        }

        $existedData = [];
        $existedCount = 0;
        $migratedCount = 0;

        DB::beginTransaction();
        try {
            foreach ($clients as $client) {
                $originalClientId = $client['id'];
                $clientCollector = DB::connection('izinew')->table('client_collectors')->where('client_id', $originalClientId)->first();
                if ($clientCollector === null) {
                    continue;
                }
                Log::info("clientCollectorId : " . $clientCollector->collector_id);
                $matchingCollectorId = $this->getMatchingCollectorId($client['collector_id']);
                $matchingAgencyId = $this->getMatchingAgencyId($client['agency_id']);
                $check_client = $this->check_client_exist($client['nom'], $client['prenoms'], $client['phone']);


                if ($matchingCollectorId !== null && $matchingAgencyId !== null) {
                    if ($check_client !== null) {
                        $existedCount++;
                        $existedData[] = [
                            'id' => $client['id'],
                            'nom' => $client['nom'],
                            'prenoms' => $client['prenoms'],
                            'phone' => $client['phone'],
                            'collector_id' => $matchingCollectorId,
                            'agency_id' => $matchingAgencyId,
                        ];
                    } else {
                        $clientId = DB::table('clients')->insertGetId([
                            'nom' => $client['nom'] ?? null,
                            'prenoms' => $client['prenoms'] ?? null,
                            'phone' => $client['phone'] ?? null,
                            'email' => $client['email'] ?? null,
                            'profession' => $client['profession'] ?? null,
                            'city_id' => $client['city_id'] ?? null,
                            'quarter_id' => $client['quarter_id'] ?? null,
                            'agency_id' => $matchingAgencyId,
                            'user_id' => $client['user_id'] ?? null,
                            'status' => $client['status'] ?? 'active',
                            'collector_id' => $matchingCollectorId,
                            'code' => $client['code'] ?? null,
                            'localisation' => json_encode($client['localisation'] ?? null),
                            'created_at' => $client['created_at'] ?? now(),
                            'updated_at' => $client['updated_at'] ?? now(),
                        ]);

                        if ($clientId !== null) {

                            if ($clientCollector) {
                                DB::table('client_collectors')->insert([
                                    'client_id' => $clientId,
                                    'collector_id' => $matchingCollectorId,
                                    'agency_id' => $matchingAgencyId,
                                    'is_principal' => true,
                                    'created_at' => $clientCollector->created_at ?? now(),
                                    'updated_at' => $clientCollector->updated_at ?? now(),
                                ]);
                            }
                            $migratedCount++;
                        }
                    }
                } else {
                    Log::warning("match collector_id ou agency_id introuvable pour le client {$client['nom']} {$client['prenoms']}");
                    Log::info("Collector_id : {$matchingCollectorId}, Agency_id : {$matchingAgencyId}");
                }
            }

            DB::commit();

            Log::info('Clients existants : ' . $existedCount);
            Log::info('Clients migrés : ' . $migratedCount);

            return response()->json([
                'success' => true,
                'message' => "{$migratedCount} client(s) migré(s) avec succès.",
                'result' => [
                    'migratedCount' => $migratedCount,
                    'existedCount' => $existedCount,
                    'existedData' => $existedData
                ],
            ], 201);
        } catch (\Throwable $th) {
            DB::rollBack();
            Log::error('Erreur de migration des clients : ' . $th->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la migration : ' . $th->getMessage(),
            ]);
        }
    }

    public function migratePacks()
    {
        $response = [];
        try {
            $packs = DB::connection('izinew')->table('packs')->get();
            $newPacks = [];
            DB::beginTransaction();
            try {
                foreach ($packs as $key => $value) {
                    $isExistPack = DB::table('packs')->where('name', $value->name)->exists();
                    if (!$isExistPack) {
                        $newPacks[] = [
                            'name' => $value->name,
                            'description' => $value->description,
                            'total_price' => $value->total_price,
                            'tarif' => $value->tarif,
                            'carnet_price' => $value->carnet_price,
                            'duration' => $value->duration,
                            'category' => $value->category,
                            'status' => $value->status,
                            'created_at' => $value->created_at,
                            'updated_at' => $value->updated_at,
                        ];
                    }
                }
                DB::table('packs')->insert($newPacks);

                DB::commit();

                $response = [
                    'success' => true,
                    'message' => 'Packs added',
                    'result' => count($newPacks)
                ];
                return response()->json($response);
            } catch (\Throwable $th) {
                DB::rollBack();
                $response = [
                    'success' => false,
                    'message' => 'Erreur lors de l\'ajout des packs',
                    'result' => null,
                    'exception' => $th->getMessage()
                ];
                return response()->json($response);
            }
        } catch (\Throwable $th) {
            $response = [
                'success' => false,
                'message' => 'Erreur lors de l\'ajout des packs',
                'result' => null,
                'exception' => $th->getMessage()
            ];
            return response()->json($response);
        }
    }

    public function getNewClientId(int $clientId)
    {
        $newClientId = null;
        try {
            $filePath = base_path('/database/clients.json');
            if (!file_exists($filePath)) {
                return response()->json(['message' => 'Fichier JSON introuvable.'], 404);
            }
            $clients = json_decode(file_get_contents($filePath), true);

            if (!is_array($clients)) {
                return response()->json(['message' => 'Données JSON invalides.'], 400);
            }
            $clients = collect($clients);
            $newClient = collect($clients)->where('id', $clientId)->first();
            if ($newClient) {
                $client = DB::table('clients')->select('id')->where('nom', $newClient['nom'])->where('prenoms', $newClient['prenoms'])->orWhere('phone', $newClient['phone'])->first();
                if ($client !== null) {
                    $newClientId = $client->id;
                }
            }
        } catch (\Throwable $th) {
            return null;
        }
        return $newClientId;
    }

    public function getMatchingPackId(int $packId)
    {
        $newPackId = null;
        $pack = DB::connection('izinew')->table('packs')->where('id', $packId)->first();
        if ($pack) {
            $newPack = DB::table('packs')->where('name', $pack->name)->first();
            if ($newPack) {
                $newPackId = $newPack->id;
            }
        }
        return $newPackId;
    }


    public function getMatchingCotisationId(int $cotisationId)
    {
        $newCotisationId = null;
        $cotisationsFile = base_path('/database/cotisations.json');
        // Vérifier si le fichier existe
        if (!file_exists($cotisationsFile)) {
            return response()->json(['success' => false, 'message' => 'Fichier cotisations.json introuvable'], 404);
        }
        $cotisations = json_decode(file_get_contents($cotisationsFile), true);
        // Vérifier si les données sont valides
        if (!is_array($cotisations)) {
            return response()->json(['success' => false, 'message' => 'Données JSON invalides'], 400);
        }
        $cotisation = collect($cotisations)->where('id', $cotisationId)->first();
        if ($cotisation !== null) {
            $NewCotisation = DB::table('cotisations')->where('id', $cotisation['id'])->first();
            if ($NewCotisation !== null) {
                $newCotisationId = $NewCotisation->id;
            }
        }
        return $newCotisationId;
    }

    public function getMatchingSubscriptionId(int $subscriptionId)
    {
        $newSubscriptionId = null;
        $subscriptionsFile = base_path('/database/subscriptions.json'); // Chemin du fichier JSON
        // Vérifier si le fichier existe
        if (!file_exists($subscriptionsFile)) {
            return response()->json(['success' => false, 'message' => 'Fichier subscriptions.json introuvable'], 404);
        }

        $subscriptions = json_decode(file_get_contents($subscriptionsFile), true);


        // Vérifier si les données sont valides
        if (!is_array($subscriptions)) {
            return response()->json(['success' => false, 'message' => 'Données JSON invalides'], 400);
        }
        $subscription = collect($subscriptions)->where('id', $subscriptionId)->first();
        if ($subscription !== null) {
            $NewSubscription = DB::table('subscriptions')->where('code', $subscription['code'])->first();
            if ($NewSubscription !== null) {
                $newSubscriptionId = $NewSubscription->id;
            }
        }
        return $newSubscriptionId;
    }

    public function migrateClientSubscriptions()
    {
        set_time_limit(10000);
        ini_set('memory_limit', '4048M');
        ini_set('output_buffering', '4096');

        $subscriptionsFile = base_path('/database/subscriptions.json');
        if (!file_exists($subscriptionsFile)) {
            return response()->json(['success' => false, 'message' => 'Fichier subscriptions.json introuvable'], 404);
        }

        $subscriptions = json_decode(file_get_contents($subscriptionsFile), true);
        if (!is_array($subscriptions)) {
            return response()->json(['success' => false, 'message' => 'Données JSON invalides'], 400);
        }

        $subscriptions = collect($subscriptions);
        try {
            DB::beginTransaction();
            $subscriptions->chunk(500)->each(function ($chunk) {
                $data = [];
                foreach ($chunk as $value) {
                    $data[] = [
                        'agency_id' => $this->getMatchingAgencyId($value['agency_id']),
                        'pack_id' => $this->getMatchingPackId($value['pack_id']),
                        'client_id' => $this->getNewClientId($value['client_id']),
                        'collector_id' => $this->getMatchingCollectorId($value['collector_id']),
                        'price' => $value['price'],
                        'carnet_price' => $value['carnet_price'],
                        'status' => $value['status'],
                        'motif' => $value['motif'] ?? null,
                        'started_at' => $value['started_at'],
                        'finished_at' => $value['finished_at'] ?? null,
                        'delivered_at' => $value['delivered_at'] ?? null,
                        'code' => $value['code'],
                        'created_at' => $value['created_at'],
                        'updated_at' => $value['updated_at'],
                    ];
                }
                DB::table('subscriptions')->insert($data);
            });
            DB::commit();
            return response()->json(['success' => true, 'message' => 'Subscriptions updated', 'result' => $subscriptions->count()], 201);
        } catch (\Throwable $th) {
            DB::rollback();
            Log::error('Error migrating subscriptions', ['exception' => $th->getMessage()]);
            return response()->json(['success' => false, 'message' => 'Erreur lors de la migration', 'error' => $th->getMessage()], 500);
        }
    }



    public function migrateClientCotisations()
    {
        set_time_limit(10000);
        ini_set('memory_limit', '4048M');
        ini_set('output_buffering', '4096');

        $cotisationsFile = base_path('/database/cotisations.json');

        // Vérifier si le fichier existe
        if (!file_exists($cotisationsFile)) {
            return response()->json(['success' => false, 'message' => 'Fichier cotisations.json introuvable'], 404);
        }

        $cotisations = json_decode(file_get_contents($cotisationsFile), true);
        if (!is_array($cotisations)) {
            return response()->json(['success' => false, 'message' => 'Données JSON invalides'], 400);
        }

        $cotisations = collect($cotisations);
        try {
            DB::beginTransaction();
            $cotisations->chunk(500)->each(function ($chunk) {
                $data = [];
                foreach ($chunk as $value) {
                    // Validation de chaque enregistrement
                    if (!$this->isValidCotisation($value)) {
                        Log::error('Données de cotisation invalides', ['cotisation' => $value]);
                        continue;
                    }
                    $agencyId = $this->getMatchingAgencyId($value['agency_id']);
                    $clientId = $this->getNewClientId($value['client_id']);
                    $collectorId = $this->getMatchingCollectorId($value['collector_id']);
                    $subscriptionId = $this->getMatchingSubscriptionId($value['subscription_id']);

                    $data[] = [
                        'agency_id' => $agencyId,
                        'client_id' => $clientId,
                        'collector_id' => $collectorId,
                        'subscription_id' => $subscriptionId,
                        'total_amount' => $value['total_amount'],
                        'nbre_cotise' => $value['nbre_cotise'],
                        'date_cotise' => $value['date_cotise'],
                        'month' => $value['month'],
                        'start_at' => $value['start_at'],
                        'end_at' => $value['end_at'] ?? null,
                        'status' => $value['status'],
                        'description' => $value['description'] ?? null,
                        'created_at' => $value['created_at'],
                        'updated_at' => $value['updated_at'],
                    ];
                }

                if (!empty($data)) {
                    DB::table('cotisations')->insert($data);
                }
            });

            DB::commit();
            return response()->json([
                'success' => true,
                'message' => 'Cotisations added',
                'result' => $cotisations->count()
            ], 201);
        } catch (\Throwable $th) {
            DB::rollback();
            Log::error('Erreur lors de la migration des cotisations', ['exception' => $th->getMessage()]);
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la mise à jour des cotisations',
                'error' => $th->getMessage()
            ], 500);
        }
    }

    /**
     * Valide les données d'une cotisation.
     *
     * @param array $data
     * @return bool
     */
    private function isValidCotisation(array $data): bool
    {
        $requiredFields = [
            'agency_id',
            'client_id',
            'collector_id',
            'subscription_id',
            'total_amount',
            'nbre_cotise',
            'date_cotise',
            'month',
            'status',
            'created_at',
            'updated_at'
        ];

        foreach ($requiredFields as $field) {
            if (!array_key_exists($field, $data)) {
                return false;
            }
        }

        return true;
    }


    public function migratePaymentData()
    {

        set_time_limit(100000000000);
        ini_set('memory_limit', '10000M');
        ini_set('output_buffering', '10000');

        try {
            // Récupération des paiements depuis la base de données source
            $payments = DB::connection('izinew')->table('payments')->get();
            if ($payments->isEmpty()) {
                return response()->json(['success' => false, 'message' => 'Aucun paiement trouvé'], 404);
            }

            // Segmentation des données pour éviter la surcharge mémoire
            $payments->chunk(500)->each(function ($chunk) {
                $data = [];
                foreach ($chunk as $value) {
                    // Validation des données de paiement
                    if (!$this->isValidPayment((array)$value)) {
                        Log::error('Données de paiement invalides', ['payment' => $value]);
                        continue;
                    }

                    $data[] = [
                        'client_id' => $this->getNewClientId($value->client_id),
                        'agency_id' => $this->getMatchingAgencyId($value->agency_id),
                        'collector_id' => $this->getMatchingCollectorId($value->collector_id),
                        'subscription_id' => $this->getMatchingSubscriptionId($value->subscription_id),
                        'pack_id' => $this->getMatchingPackId($value->pack_id),
                        'cotisation_id' => $value->payment_type === 'COLLECT' ? $this->getMatchingCotisationId($value->cotisation_id) : null,
                        'amount' => $value->amount,
                        'payment_date' => $value->payment_date,
                        'key' => $value->key,
                        'month' => $value->month,
                        'metadata' => json_encode($value->metadata),
                        'status' => $value->status,
                        'payment_mode' => $value->payment_mode,
                        'payment_type' => $value->payment_type,
                        'description' => $value->description ?? null,
                        'created_at' => $value->created_at,
                        'updated_at' => $value->updated_at,
                        'versement_id' => $value->versement_id,
                    ];
                }

                // Insertion en base si le lot contient des données
                if (!empty($data)) {
                    DB::table('payments')->insert($data);
                }
            });

            DB::commit();
            return response()->json([
                'success' => true,
                'message' => 'Payments added successfully',
                'result' => $payments->count()
            ], 201);
        } catch (\Throwable $th) {
            DB::rollback();
            Log::error('Erreur lors de la migration des paiements', ['exception' => $th->getMessage()]);
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la migration des paiements',
                'error' => $th->getMessage()
            ], 500);
        }
    }

    /**
     * Valide les données d'un paiement.
     *
     * @param array $data
     * @return bool
     */
    private function isValidPayment(array $data): bool
    {
        $requiredFields = [
            'client_id',
            'agency_id',
            'collector_id',
            'subscription_id',
            'pack_id',
            'cotisation_id',
            'amount',
            'payment_date',
            'key',
            'month',
            'metadata',
            'status',
            'payment_mode',
            'payment_type',
            'created_at',
            'updated_at'
        ];

        foreach ($requiredFields as $field) {
            if (!array_key_exists($field, $data)) {
                return false;
            }
        }

        return true;
    }

    public function fetchNewUserId(int $userId){
        $newUserId = null;
        try {
            $filePath = base_path('/database/users.json');
            if (!file_exists($filePath)) {
                return response()->json(['message' => 'Fichier JSON introuvable.'], 404);
            }
            $users = json_decode(file_get_contents($filePath), true);

            if (!is_array($users)) {
                return response()->json(['message' => 'Données JSON invalides.'], 400);
            }
            $users = collect($users);
            $newUser = collect($users)->where('id', $userId)->first();
            if ($newUser) {
                $user = DB::table('users')->select('id')->where('username', $newUser['username'])->orWhere('phone', $newUser['phone'])->first();
                if ($user !== null) {
                    $newUserId = $user->id;
                }
            }
        } catch (\Throwable $th) {
            return null;
        }
        return $newUserId;
    }

    public function checkExistWallet(int $userId){
        $isExist = DB::table('wallets')->where('user_id', $userId)->exists();
        return $isExist;
    }

    public function getUserWallet(int $userId){
        $isExist = $this->checkExistWallet($userId);
        if($isExist){
            $wallet = DB::table('wallets')->where('user_id', $userId)->first();
            return $wallet;
        }
        return null;
    }

    public function updatePersonalWalletBalance(){
        try {
            $data = [];
            $filePath = base_path('/database/wallets.json');
            if (!file_exists($filePath)) {
                return response()->json(['message' => 'Fichier JSON introuvable.'], 404);
            }
            $wallets = json_decode(file_get_contents($filePath), true);
            if (!is_array($wallets)) {
                return response()->json(['message' => 'Données JSON invalides.'], 400);
            }
            $wallets = collect($wallets);
            $wallets->chunk(100)->each(function ($chunk) {
                foreach ($chunk as $value) {
                    $userId = $this->getNewUserId($value['user_id']);
                    if($userId){
                        $wallet = $this->getUserWallet($userId);
                        if($wallet){
                            $newBalance = $value['balance'] + $wallet->balance;
                            $newSolde = $value['solde'] + $wallet->solde;
                            $up = DB::table('wallets')->where('user_id', $userId)->update([
                                'balance' => $newBalance,
                                'solde' => $newSolde,
                            ]);
                            if($up){
                                $data[] = [
                                    'user_id' => $userId,
                                    'balance' => $newBalance,
                                    'solde' => $newSolde,
                                    'wallet_code' => $wallet->code,
                                ];
                            }
                        }
                    }
                }
            });
            DB::commit();
            return response()->json(['message' => 'Wallets updated', 'result' => $data], 201);
        } catch (\Throwable $th) {
            DB::rollback();
            Log::error('Erreur de mise à jour des wallets', ['exception' => $th->getMessage()]);
            return response()->json(['message' => 'Erreur lors de la mise à jour des wallets', 'error' => $th->getMessage()], 500);
        }
    }
}
