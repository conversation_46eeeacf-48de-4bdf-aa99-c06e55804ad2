<?php

namespace App\Http\Controllers\API\root;

use App\Http\Controllers\API\helpers\HelperController;
use App\Models\Cotisation;
use App\Models\Payment;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class RootController extends HelperController
{

    /**
     * updateExistingRecords function
     *  Mettre à jour les enregistrements avec le versement_id
     * @param Request $request
     * @param Request $request
     * @return JsonResponse $response
     */
    public function updateExistingRecords(Request $request)
    {
        $response = [];
        $status = 200;
        DB::beginTransaction();
        try {
            // Récupérer tous les versements confirmés
            $versements = DB::table('versements')->where('status', 'confirmed')->get();
            $updatedPayments = collect();
            $updatedSubscriptions = collect();

            foreach ($versements as $versement) {
                $collector_id = $versement->collector_id;
                $payment_date = Carbon::parse($versement->payment_date);

                // Récupérer les ventes de carnets non associées au versement pour ce collecteur avant ou à la date du versement
                $subscriptions = DB::table('payments')
                    ->where('collector_id', $collector_id)
                    ->whereNull('versement_id')
                    ->whereDate('created_at', '<=', $payment_date)
                    ->whereNotNull('subscription_id')
                    ->where('payment_type', 'CARNET')
                    ->get();

                // Récupérer les collectes de cotisations non associées au versement pour ce collecteur avant ou à la date du versement
                $payments = DB::table('payments')
                    ->where('collector_id', $collector_id)
                    ->whereNull('versement_id')
                    ->whereDate('payment_date', '<=', $payment_date)
                    ->whereNotNull('cotisation_id')
                    ->where('payment_type', 'COLLECT')
                    ->get();

                // Mettre à jour les enregistrements avec le versement_id
                DB::table('payments')
                    ->whereIn('id', $subscriptions->pluck('id'))
                    ->update([
                        'versement_id' => $versement->id,
                        'status' => 'paid',
                    ]);

                DB::table('payments')
                    ->whereIn('id', $payments->pluck('id'))
                    ->update([
                        'versement_id' => $versement->id,
                        'status' => 'paid',
                    ]);

                // Accumuler les résultats des mises à jour
                $updatedSubscriptions = $updatedSubscriptions->merge($subscriptions);
                $updatedPayments = $updatedPayments->merge($payments);
            }

            // Commit toutes les mises à jour une fois toutes les opérations terminées
            DB::commit();

            $response = [
                'success' => true,
                'message' => "Mise à jour des enregistrements terminée avec succès.",
                'result' => [
                    'versements' => $versements,
                    'updated_payments' => $updatedPayments,
                    'updated_subscriptions' => $updatedSubscriptions,
                ],
                'errors' => null,
            ];
        } catch (\Throwable $th) {
            DB::rollback();
            $response = [
                'success' => false,
                'message' => "Échec de la mise à jour des enregistrements",
                'result' => null,
                'errors' => $th->getMessage(),
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    /**
     * refacExistingCotisationsPayments function
     *  Mettre à jour les payments du type COLLECT sous le nouveau format
     * @param Request $request
     * @param Request $request
     * @return JsonResponse $response
     */
    public function refacExistingCotisationsPayments(Request $request)
    {

        set_time_limit(10000);
        ini_set('memory_limit', '4048M');
        ini_set('output_buffering', '4096');
        $response = [];
        $status = 200;
        // $cotisation_id = 45; # for testing a specific cotisation
        DB::beginTransaction();
        Log::info("refac existing payments");
        try {
            // get payments
            // $payments = Payment::where('payment_type', 'COLLECT')->where('cotisation_id', $cotisation_id)->get(); # for testing a specific cotisation
            $payments = Payment::where('payment_type', 'COLLECT')->get();
            // dd($payments);
            $data = collect($payments);
            $initial_payments_count = count($data);
            // Étape 1: Grouper par "cotisation_id"
            $groupedByCotisation = $data->groupBy('cotisation_id');

            // Étape 2: Pour chaque groupe de "cotisation_id", grouper par "month"
            $result = $groupedByCotisation->map(function ($items) {
                return $items->groupBy('month')->map(function ($monthItems) {
                    // Transformer les éléments de ce groupe en tableau dans la clé "metadata"
                    $metadata = $monthItems->map(function ($item) use ($monthItems) {
                        return [
                            'id' => Str::uuid()->toString(),
                            'month' => $item['month'],
                            'key' => $item['key'],
                            'amount' => $item['amount'],
                            'payment_date' => $item['payment_date'],
                            'payment_mode' => 'CASH',
                            'status' => $item['status'],
                            'versement_id' => $item['versement_id'],
                        ];
                    })->toArray();

                    $metadataCollection = collect($metadata);
                    $month_status = 'pending';
                    if ((count($metadata) == 31 && $metadataCollection->map(function ($item) {
                        return $item['status'];
                    })->contains('paid'))) {
                        $month_status = 'paid';
                    } else if ((count($metadata) == 31 && $metadataCollection->map(function ($item) {
                        return $item['status'];
                    })->contains('completed'))) {
                        $month_status = 'completed';
                    }
                    // Créer une nouvelle structure de tableau pour ce groupe
                    return [
                        'id' => $monthItems[0]['id'], # or $monthItems[count($monthItems)-1]['id'],
                        'month' => $monthItems[count($monthItems) - 1]['month'],
                        'payment_date' => $monthItems[count($monthItems) - 1]['payment_date'],
                        'amount' => count($monthItems) * $monthItems[count($monthItems) - 1]['amount'],
                        // 'metadata_count' => count($metadata), # for test purpose
                        // 'last_month_payment_id' => $monthItems[count($monthItems)-1]['id'], # for test purpose
                        // 'first_month_payment_id' => $monthItems[0]['id'], # for test purpose
                        'metadata' => json_encode($metadata, JSON_THROW_ON_ERROR),
                        'status' => $month_status,
                        'subscription_id' => $monthItems[count($monthItems) - 1]['subscription_id'],
                        'agency_id' => $monthItems[count($monthItems) - 1]['agency_id'],
                        'collector_id' => $monthItems[count($monthItems) - 1]['collector_id'],
                        'cotisation_id' => $monthItems[count($monthItems) - 1]['cotisation_id'],
                        'pack_id' => $monthItems[count($monthItems) - 1]['pack_id'],
                        'payment_mode' => $monthItems[count($monthItems) - 1]['payment_mode'],
                        'payment_type' => $monthItems[count($monthItems) - 1]['payment_type'],
                        'description' => $monthItems[count($monthItems) - 1]['description'],
                        'created_at' => $monthItems[count($monthItems) - 1]['created_at'],
                        'updated_at' => $monthItems[count($monthItems) - 1]['updated_at'],
                        'client_id' => $monthItems[count($monthItems) - 1]['client_id'],
                    ];
                });
            })->values()->flatten(1);

            # Delete $payments
            $del = $payments->each->delete();
            if ($del) {
                // Diviser l'insertion en lots (batches)
                $batchSize = 500; // Taille du lot, ajustable selon la configuration
                $resultChunks = $result->chunk($batchSize);

                foreach ($resultChunks as $chunk) {
                    Payment::insert($chunk->toArray());
                }

                DB::commit();
                $response = [
                    'success' => true,
                    'message' => "Refac des cotisations avec succès.",
                    'initial_payments_count' => $initial_payments_count,
                    'result_count' => count($result),
                    'errors' => null,
                ];
            } else {
                DB::rollback();
                $response = [
                    'success' => false,
                    'message' => "Échec de la récupération des enregistrements",
                    'result' => null,
                    'errors' => "Erreur lors de la suppression des anciens enregistrements",
                ];
            }
        } catch (\Throwable $th) {
            DB::rollback();
            Log::error("erreur refact" . $th->getMessage());
            $status = 500;
            $response = [
                'success' => false,
                'message' => "Échec de la récupération des enregistrements",
                'result' => null,
                'errors' => $th->getMessage(),
            ];
        }
        return $this->apiResponse($response, $status);
    }

    public function resetExistingSubscriptionsCode(Request $request) {
        set_time_limit(10000);
        ini_set('memory_limit', '4048M');
        ini_set('output_buffering', '4096');
        $response = [];
        $status = 200;
        Log::info("resetExistingSubscriptionsCode start");

        DB::beginTransaction();

        try {
            // get subscriptions
            $subscriptions = Subscription::all();

            foreach ($subscriptions as $subscription) {
                # get subscription client
                $client = $subscription->client;

                $code = $this->generate_sub_code($client->nom);
                $subscription->code = mb_convert_encoding($code, 'UTF-8', mb_list_encodings()); // Ensure UTF-8 encoding
                $subscription->timestamps = false;
                $subscription->save();
            }

            DB::commit();

            $response = [
                'success' => true,
                'message' => "Codes des abonnements réinitialisés avec succès.",
                'result' => null,
                'errors' => null,
            ];
        } catch (\Throwable $th) {
            DB::rollback();
            $response = [
                'success' => false,
                'message' => "Échec de la réinitialisation des codes des abonnements",
                'result' => null,
                'errors' => $th->getMessage(),
            ];
        }

        Log::info("resetExistingSubscriptionsCode end");
        return $this->apiResponse($response, $status);
    }

    public function getCollectorPaymentIncorrect(Request $request)
    {
        $response = [];
        $status = 200;
        DB::beginTransaction();
        try {
            $subscriptions = Subscription::all();
            $months = collect(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12);
            $data = [];
            foreach ($subscriptions as $key => $sub) {
                $payments = Payment::where('subscription_id', $sub->id)->where('payment_type', 'COLLECT')->get();
                foreach ($payments as $key => $payment) {
                    $key = $payment->key;
                    $month = $payment->month;
                    $resP = Payment::where('subscription_id', $sub->id)->where('payment_type', 'COLLECT')->where('key', $key)->where('month', $month)->count();
                    if ($resP > 1) {
                        $data[] = [
                            'subscription_id' => $sub->id,
                            'payment_type' => 'COLLECT',
                            'key' => $key,
                            'month' => $month,
                            'count' => $resP,
                        ];
                    }
                }
            }
            $response = [
                'success' => true,
                'message' => "Etat des paiements incorrectes",
                'result' => $data,
            ];
        } catch (\Throwable $th) {
            DB::rollback();
            $response = [
                'success' => false,
                'message' => "Échec de la récupération des enregistrements",
                'result' => null,
                'errors' => $th->getMessage(),
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function getCollectorPaymentIncorrectV2(Request $request)
    {
        $response = [];
        $status = 200;
        DB::beginTransaction();
        try {
            // Récupérer tous les paiements de type 'COLLECT' en une seule requête
            $payments = Payment::where('payment_type', 'COLLECT')->get();

            // Regrouper les paiements par abonnement, key et month
            $groupedPayments = $payments->groupBy(function ($payment) {
                return $payment->subscription_id . '-' . $payment->key . '-' . $payment->month;
            });

            // Filtrer pour ne garder que ceux qui ont plus d'une entrée
            $data = $groupedPayments->filter(function ($group) {
                return $group->count() > 1;
            })->map(function ($group) {
                // Récupérer le premier paiement pour obtenir les détails du groupe
                $firstPayment = $group->first();
                return [
                    'subscription_id' => $firstPayment->subscription_id,
                    'payment_type' => 'COLLECT',
                    'key' => $firstPayment->key,
                    'month' => $firstPayment->month,
                    'count' => $group->count(),
                ];
            })->values()->toArray();

            $response = [
                'success' => true,
                'message' => "État des paiements incorrects",
                'result' => $data,
            ];
        } catch (\Throwable $th) {
            DB::rollback();
            $response = [
                'success' => false,
                'message' => "Échec de la récupération des enregistrements",
                'result' => null,
                'errors' => $th->getMessage(),
            ];
            $status = 500;
        }
        return $this->apiResponse($response, $status);
    }

    public function addUserSecretCode(Request $request)
    {
        $response = [];
        DB::beginTransaction();
        try {
            // Assurez-vous que le fichier existe avant de le lire
            $filePath = database_path('users.json');
            if (file_exists($filePath)) {
                $file = file_get_contents($filePath);
                // Décoder le contenu du fichier JSON
                $jsonData = json_decode($file, true);

                // Vérifiez que la clé 'users' existe dans le JSON
                if (isset($jsonData['users']) && is_array($jsonData['users'])) {
                    $users = $jsonData['users'];
                    $data = [];

                    // Parcourir les utilisateurs
                    foreach ($users as $value) {
                        // Vérifiez si l'utilisateur a bien une clé 'user_id'
                        if (isset($value['user_id'])) {
                            $resU = User::where('id', $value['user_id'])->first();
                            if ($resU) {
                                $resU->secret_code = Crypt::encryptString($value['secret_code']);
                                $resU->save();
                                $data[] = [
                                    'user_id' => $resU->id, // Utilisez $resU->id au lieu de $resU->user_id
                                    'username' => $resU->username,
                                    'phone' => $resU->phone,
                                    'code' => $value['secret_code'],
                                    'secret_code' => $resU->secret_code,
                                ];
                            }
                        }
                    }
                    if (count($data) > 0) {
                        DB::commit();
                    }

                    $response = [
                        'success' => true,
                        'message' => "Secret code ajouté aux utilisateurs avec succès",
                        'result' => $data,
                    ];
                } else {
                    // Gérer le cas où la clé 'users' n'existe pas
                    $response = [
                        'success' => false,
                        'message' => "Format JSON invalide ou clé 'users' manquante",
                        'result' => null,
                    ];
                }
            } else {
                // Gérer le cas où le fichier n'existe pas
                $response = [
                    'success' => false,
                    'message' => "Fichier JSON introuvable",
                    'result' => null,
                ];
            }
        } catch (\Throwable $th) {
            DB::rollback();
            $response = [
                'success' => false,
                'message' => "Échec de la récupération des enregistrements",
                'result' => null,
                'errors' => $th->getMessage(),
            ];
        }

        return $this->apiResponse($response, 200);
    }

    /**
     * addCotisationIdToCollectPayments function
     *  Mettre à jour les payments du type COLLECT
     * @param Request $request
     * @param Request $request
     * @return JsonResponse $response
     */
    public function addCotisationIdToCollectPayments(Request $request)
    {
        set_time_limit(10000);
        ini_set('memory_limit', '4048M');
        ini_set('output_buffering', '4096');

        $status = 200;
        $response = [];

        DB::beginTransaction();
        Log::info("Refactor existing payments");

        try {
            // Récupération des paiements avec "payment_type" COLLECT
            $payments = Payment::where('payment_type', 'COLLECT')->get();
            $initial_payments_count = $payments->count();
            $nbre_payment_processed = 0;

            // Grouper les paiements par "subscription_id"
            $payments->groupBy('subscription_id')->each(function ($payments, $subscription_id) use (&$nbre_payment_processed) {
                $nbre_cotise = 0;
                $total_amount = 0;
                $date_cotise = null;
                $month = null;
                $end_at = null;
                $description = null;

                // Dernier paiement
                $last_payment = $payments[count($payments) - 1];
                if ($last_payment) {
                    $month = $last_payment->month;
                }

                // Récupération de la cotisation correspondante
                $cotisation = Cotisation::where('subscription_id', $subscription_id)->first();

                // Calcul du nombre de cotisations et du montant total
                $payments->each(function ($payment) use (&$nbre_cotise, &$total_amount, &$cotisation, &$nbre_payment_processed) {
                    $metadata = $payment->metadata;
                    $nbre_cotise += count($metadata);
                    $total_amount += array_sum(array_column($metadata, 'amount'));

                    // Mise à jour du cotisation_id dans le paiement
                    if ($payment->update(['cotisation_id' => $cotisation->id, 'updated_at' => $payment->updated_at])) {
                        $nbre_payment_processed++;
                    }
                });

                // Metadonnées du dernier paiement
                if ($last_payment) {
                    $last_metadata = $last_payment->metadata;
                    $last_payment_date = Carbon::parse($last_metadata[count($last_metadata) - 1]['payment_date']);
                    $date_cotise = $last_payment_date->format('Y-m-d H:i:s');
                    $description = "Cotisation du " . $date_cotise;

                    if ($last_metadata[count($last_metadata) - 1]['month'] == 12) {
                        $end_at = $last_payment_date->format('Y-m-d H:i:s');
                    }
                }

                // Mise à jour de la cotisation
                $status = $nbre_cotise == 372 ? "finished" : $cotisation->status;
                $cotisation_up = $cotisation->update([
                    'nbre_cotise' => $nbre_cotise,
                    'total_amount' => $total_amount,
                    'date_cotise' => $date_cotise,
                    'month' => $month,
                    'end_at' => $end_at,
                    'description' => $description,
                    'status' => $status,
                    'updated_at' => $payments[count($payments) - 1]->updated_at,
                ]);

                if (!$cotisation_up) {
                    DB::rollback();
                    throw new \Exception("Erreur lors de la mise à jour des cotisations");
                }
            });

            // Commit après avoir traité toutes les cotisations
            DB::commit();

            $response = [
                'success' => true,
                'message' => "Cotisation ajoutée avec succès",
                'result' => [
                    'initial_payments_count' => $initial_payments_count,
                    'nbre_payment_processed' => $nbre_payment_processed,
                ],
            ];

        } catch (\Throwable $th) {
            Log::error("Erreur refact: " . $th->getMessage());
            DB::rollback();
            $status = 500;
            $response = [
                'success' => false,
                'message' => "Échec de la récupération des enregistrements",
                'errors' => $th->getMessage(),
            ];
        }

        return $this->apiResponse($response, $status);
    }

    /**
     * refacCollectPayments function
     *  Mettre à jour les payments du type COLLECT
     * @param Request $request
     * @param Request $request
     * @return JsonResponse $response
     */
    public function refacCollectPayments(Request $request)
    {
        set_time_limit(10000);
        ini_set('memory_limit', '4048M');
        ini_set('output_buffering', '4096');

        $status = 200;
        $response = [];

        DB::beginTransaction();
        Log::info("refacCollectPayments started");

        try {
            // Récupération des paiements avec "payment_type" COLLECT
            $payments = Payment::where('payment_type', 'COLLECT')->get();
            // Log::info("Payments: " . json_encode($payments->count()));
            $initial_payments_count = $payments->count();
            $nbre_payment_processed = 0;

            // Grouper les paiements par "subscription_id"
            $payments->groupBy('subscription_id')->each(function ($payments, $subscription_id) use (&$nbre_payment_processed) {

                // Récupération de la cotisation correspondante
                $cotisation = Cotisation::where('subscription_id', $subscription_id)->first();

                // Mise à jour de chaque paiement
                $payments->each(function ($payment) use (&$nbre_payment_processed) {
                    $metadata = $payment->metadata;

                    // Ajouter 'payment_mode' = 'CASH' à tous les $metadata
                    $metadata = array_map(function ($item, int $index) {
                        return [
                            'id' => $item['id'],
                            'key' => $item['key'],
                            'month' => $item['month'],
                            'amount' => $item['amount'],
                            'payment_date' => $item['payment_date'],
                            'payment_mode' => 'CASH',
                            'status' => $item['status'],
                            'versement_id' => $item['versement_id'],
                        ];
                    }, $metadata, array_keys($metadata));

                    $first_metadata = $metadata[0];
                    $last_metadata = $metadata[count($metadata) - 1];
                    $first_metadata_date = Carbon::parse($first_metadata['payment_date']);
                    $last_metadata_date = Carbon::parse($last_metadata['payment_date']);

                    // Désactiver temporairement les timestamps pour la mise à jour
                    $payment->timestamps = false;

                    // Mise à jour du paiement
                    $up_payment = $payment->update([
                        'metadata' => $metadata,
                        'created_at' => $first_metadata_date->format('Y-m-d H:i:s'),
                        'updated_at' => $last_metadata_date->format('Y-m-d H:i:s'),
                    ]);

                    if ($up_payment) {
                        $nbre_payment_processed++;
                    }

                    // Réactiver les timestamps après la mise à jour
                    $payment->timestamps = true;
                });

                $last_payment = $payments[count($payments) - 1];
                $first_payment = $payments[0];

                $last_payment_last_metadata = $last_payment->metadata[count($last_payment->metadata) - 1];
                $first_payment_first_metadata = $first_payment->metadata[0];

                // Désactiver temporairement les timestamps pour la mise à jour de la cotisation
                $cotisation->timestamps = false;

                // Mise à jour de la cotisation
                $cotisation_up = $cotisation->update([
                    'description' => "Cotisation du {$last_payment_last_metadata['payment_date']}",
                    'month' => $last_payment_last_metadata['month'],
                    'start_at' => Carbon::parse($first_payment_first_metadata['payment_date'])->format('Y-m-d H:i:s'),
                    'end_at' => Carbon::parse($last_payment_last_metadata['payment_date'])->format('Y-m-d H:i:s'),
                    'created_at' => Carbon::parse($first_payment_first_metadata['payment_date'])->format('Y-m-d H:i:s'),
                    'updated_at' => Carbon::parse($last_payment_last_metadata['payment_date'])->format('Y-m-d H:i:s'),
                ]);

                if (!$cotisation_up) {
                    DB::rollback();
                    throw new \Exception("Erreur lors de la mise à jour des cotisations");
                }

                // Réactiver les timestamps après la mise à jour
                $cotisation->timestamps = true;
            });

            // Commit après avoir traité toutes les cotisations
            DB::commit();

            $response = [
                'success' => true,
                'message' => "Cotisation ajoutée avec succès",
                'result' => [
                    'initial_payments_count' => $initial_payments_count,
                    'nbre_payment_processed' => $nbre_payment_processed,
                ],
            ];

        } catch (\Throwable $th) {
            Log::error("Erreur refact: " . $th->getMessage());
            DB::rollback();
            $status = 500;
            $response = [
                'success' => false,
                'message' => "Échec de la récupération des enregistrements",
                'errors' => $th->getMessage(),
            ];
        }

        Log::info("refacCollectPayments finished");

        return $this->apiResponse($response, $status);
    }

}
