<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CategoryTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Alimentation',
                'description' => 'Alimentation',
                'image' => 'alimentation.png',
            ],
            [
                'name' => 'Automobile',
                'description' => 'Automobile',
                'image' => 'automobile.png',
            ],
            [
                'name' => 'Beauté',
                'description' => 'Beauté',
                'image' => 'beaute.png',
            ],
            [
                'name' => 'Bijoux',
                'description' => 'Bijoux',
                'image' => 'bijoux.png',
            ],
            [
                'name' => 'Bricolage',
                'description' => 'Bricolage',
                'image' => 'bricolage.png',
            ],
            [
                'name' => 'Cadeaux',
                'description' => 'Cadeaux',
                'image' => 'cadeaux.png',
            ],
            [
                'name' => 'Café',
                'description' => 'Café',
                'image' => 'cafe.png',
            ],
            [
                'name' => 'Camping',
                'description' => 'Camping',
                'image' => 'camping.png',
            ],
            [
                'name' => 'Cinéma',
                'description' => 'Cinéma',
                'image' => 'cinema.png',
            ],
            [
                'name' => 'Cigarette',
                'description' => 'Cigarette',
                'image' => 'cigarette.png',
            ],
            [
                'name' => 'Cuisine',
                'description' => 'Cuisine',
                'image' => 'cuisine.png',
            ],
            [
                'name' => 'Décoration',
                'description' => 'Décoration',
                'image' => 'decoration.png',
            ],
            [
                'name' => 'Electroménager',
                'description' => 'Electroménager',
                'image' => 'electromenager.png',
            ],
            [
                'name' => 'Electronique',
                'description' => 'Electronique',
                'image' => 'electronique.png',
            ],
            [
                'name' => 'Enfants',
                'description' => 'Enfants',
                'image' => 'enfants.png',
            ],
        ];

        foreach ($categories as $category) {
            \App\Models\Category::create($category);
        }
    }
}
