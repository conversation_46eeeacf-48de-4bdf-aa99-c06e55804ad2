<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CountryTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $countries = [
            [
                'name' => 'Bénin',
                'code' => 'BJ',
                'prefix' => '+229',
            ],
            [
                'name' => 'Burkina Faso',
                'code' => 'BF',
                'prefix' => '+226',
            ],
            [
                'name' => 'Côte d\'Ivoire',
                'code' => 'CI',
                'prefix' => '+225',
            ],
            [
                'name' => 'Mali',
                'code' => 'ML',
                'prefix' => '+223',
            ],

            [
                'name' => 'Ghana',
                'code' => 'GH',
                'prefix' => '+233',
            ],
            [
                'name' => 'Togo',
                'code' => 'TG',
                'prefix' => '+228',
            ],
        ];

        foreach ($countries as $country) {
            \App\Models\Country::create($country);
        }
    }
}
