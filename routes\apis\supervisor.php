<?php
use Illuminate\Support\Facades\Route;

Route::group(['namespace' => "agence", 'prefix' => 'agency', 'middleware' => ['auth:sanctum',]], function () {
    //SUPERVISOR
    Route::group(['prefix'=>'supervisor','middleware'=>['auth:sanctum']],function(){
        Route::get('/collectors','SupervisorController@get_collectors');
        Route::post('/collectors/detail','SupervisorController@get_collector_detail');
        Route::get('/collectors/clients','SupervisorController@get_collector_clients');
        Route::get('/collectors/clients/subscription','SupervisorController@get_detail_subscription');
        Route::post('/collectors/clients/verify','SupervisorController@verify_client_subscription');
        Route::post('/collectors/clients/control','SupervisorController@make_control_client');
        Route::get('/controls','SupervisorController@get_controls');
    });
});
